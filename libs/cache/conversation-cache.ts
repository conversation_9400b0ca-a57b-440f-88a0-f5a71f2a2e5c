/**
 * Conversation-specific cache implementation
 * Uses the generic cache with conversation-optimized types and methods
 */

import { Cache } from './index'
import type { CachedMessage, CachedConversation } from '@/types/chat'
import { CACHE_CONFIG } from '@/constants/chat'

type ConversationCacheEntry = {
  messages: CachedMessage[]
  conversation?: CachedConversation
}

// Create conversation cache instance
const conversationCache = new Cache<ConversationCacheEntry>({
  defaultTtlMs: CACHE_CONFIG.DEFAULT_TTL_MS,
  maxEntries: CACHE_CONFIG.MAX_ENTRIES,
  maxTtlMs: CACHE_CONFIG.MAX_TTL_MS,
})

/**
 * Get cached messages for a conversation
 */
export function getCachedMessages(conversationId: string): CachedMessage[] | null {
  const entry = conversationCache.get(conversationId)
  return entry?.messages || null
}

/**
 * Get cached conversation metadata
 */
export function getCachedConversation(conversationId: string): CachedConversation | null {
  const entry = conversationCache.get(conversationId)
  return entry?.conversation || null
}

/**
 * Set cached messages for a conversation
 */
export function setCachedMessages(
  conversationId: string,
  messages: CachedMessage[],
  conversation?: CachedConversation
): void {
  const existingEntry = conversationCache.get(conversationId)
  
  conversationCache.set(conversationId, {
    messages,
    conversation: conversation || existingEntry?.conversation,
  })
}

/**
 * Set cached conversation metadata
 */
export function setCachedConversation(
  conversationId: string,
  conversation: CachedConversation
): void {
  const existingEntry = conversationCache.get(conversationId)
  
  conversationCache.set(conversationId, {
    messages: existingEntry?.messages || [],
    conversation,
  })
}

/**
 * Delete conversation from cache
 */
export function deleteCachedConversation(conversationId: string): boolean {
  return conversationCache.del(conversationId)
}

/**
 * Clear all cached conversations
 */
export function clearConversationCache(): void {
  conversationCache.clear()
}

/**
 * Get cache statistics
 */
export function getConversationCacheStats() {
  return {
    size: conversationCache.size(),
    keys: conversationCache.keys(),
  }
}
import { streamText } from 'ai'
import { MockLanguageModelV1 } from 'ai/test'
import { persistConversationTurn } from '@/app/server-actions/ai-chat'
import { NextResponse } from 'next/server'
import { AIPaneChatRequest } from '@/types/chat'

export const maxDuration = 60

// -----------------------------------------------------------------------------
// Streaming speed configuration (in milliseconds)
// Adjust these values to tune how fast the simulated chat streams.
// -----------------------------------------------------------------------------
const STREAM_INITIAL_DELAY_MS = 100 // delay before the first chunk (was 200)
const STREAM_CHUNK_DELAY_MS = 20 // delay between subsequent chunks (was 80)

// Simulated chat responses with markdown support
function getSimulatedChatResponse(
  userMessage: string,
  context?: string
): string {
  const responses = [
    `# Response to: "${userMessage}"

Based on your message, here are some insights:

## Key Points
- This is a **simulated response** to demonstrate chat functionality
- The system can handle *rich markdown* formatting
- Context awareness: ${context ? 'Context provided' : 'No context'}

## Analysis
Your message "${userMessage}" suggests you're interested in exploring the chat capabilities. This simulator can handle:

1. **Streaming responses** with realistic timing
2. **Markdown formatting** including tables and lists
3. **Context integration** from selected nodes
4. **Rich text display** when completed

\`\`\`javascript
// Example code block
const response = "This demonstrates code highlighting";
console.log(response);
\`\`\`

> This is a blockquote showing how various markdown elements are supported.

*This completes the simulated response.*`,

    `## Analyzing Your Input: "${userMessage}"

Thank you for your message! Let me provide a comprehensive response:

### Understanding Your Request
Your input suggests you're testing the chat functionality. Here's what I can help with:

| Feature | Status | Description |
|---------|--------|-------------|
| Streaming | ✅ | Real-time response generation |
| Markdown | ✅ | Rich text formatting support |
| Context | ${context ? '✅' : '❌'} | Integration with drag tree nodes |

### Response Generation
I can provide various types of responses:

- **Informational**: Explaining concepts and providing details
- **Analytical**: Breaking down complex topics
- **Creative**: Generating ideas and solutions
- **Technical**: Code examples and implementation details

### Next Steps
Feel free to continue the conversation! I can maintain context and provide relevant follow-up responses.

🎯 *This is a simulated response designed to test the chat interface.*`,

    `# Comprehensive Analysis

## Your Message
"${userMessage}"

### Context Integration
${context ? `Based on the provided context:\n\n${context.substring(0, 200)}${context.length > 200 ? '...' : ''}` : 'No additional context was provided for this conversation.'}

### Detailed Response

#### Processing Steps
1. **Message Analysis**: Examined your input for intent and content
2. **Context Review**: ${context ? 'Integrated relevant context information' : 'Noted absence of context'}
3. **Response Generation**: Crafted this comprehensive reply

#### Key Insights
- The chat system supports **real-time streaming**
- Messages can include rich formatting and structure
- Context from drag tree nodes enhances response quality

#### Technical Capabilities
\`\`\`markdown
- Markdown formatting support
- Code block highlighting
- Table generation
- List structures
- Emphasis and styling
\`\`\`

### Conclusion
This simulated response demonstrates the chat system's capabilities while providing a meaningful interaction experience.

---
*Generated in simulation mode for testing purposes.*`,
  ]

  return responses[Math.floor(Math.random() * responses.length)]
}

// Check if model supports thinking
function isThinkingModel(model: string): boolean {
  const thinkingModels = ['o1', 'o3', 'reasoning', 'claude-3.5-sonnet-thinking']
  return thinkingModels.some(tm => model.toLowerCase().includes(tm))
}

// Generate realistic thinking time based on message complexity
function getThinkingTime(userMessage: string): number {
  const baseTime = 8 // minimum 8 seconds
  const complexityFactors = [
    userMessage.length > 100 ? 5 : 0,
    userMessage.includes('?') ? 3 : 0,
    userMessage.split(' ').length > 20 ? 4 : 0,
    /analyze|compare|explain|research/.test(userMessage.toLowerCase()) ? 8 : 0,
    /complex|difficult|challenging/.test(userMessage.toLowerCase()) ? 6 : 0,
  ]
  const complexity = complexityFactors.reduce((sum, factor) => sum + factor, 0)
  return Math.min(baseTime + complexity, 28) // max 28 seconds
}

// Generate multiple sequential tool calls
function generateToolCalls(userMessage: string): string[] {
  const messageWords = userMessage.toLowerCase()

  // Context-aware tool calls based on message content
  const contextualCalls: string[] = []

  // Web search for general queries
  if (
    messageWords.includes('what') ||
    messageWords.includes('how') ||
    messageWords.includes('when')
  ) {
    contextualCalls.push(
      `🌐 Searched the web\n  ↳ Query: ${userMessage.substring(0, 50)}...\n  ↳ Found: ${Math.floor(Math.random() * 20) + 10} relevant articles\n  ↳ Sources: Multiple reliable sources verified`
    )
  }

  // Data analysis for comparative queries
  if (
    messageWords.includes('compare') ||
    messageWords.includes('vs') ||
    messageWords.includes('difference')
  ) {
    contextualCalls.push(
      `📊 Analyzed comparative data\n  ↳ Processing: Key differences and similarities\n  ↳ Timeframe: Recent data (2024-2025)\n  ↳ Result: Comprehensive comparison analysis completed`
    )
  }

  // Calculations for numerical queries
  if (
    messageWords.includes('calculate') ||
    messageWords.includes('cost') ||
    messageWords.includes('price')
  ) {
    contextualCalls.push(
      `🧮 Performed calculations\n  ↳ Computing: Relevant metrics and figures\n  ↳ Variables: Market rates, current trends, historical data\n  ↳ Output: Detailed numerical analysis provided`
    )
  }

  // Information retrieval for specific topics
  if (
    messageWords.includes('about') ||
    messageWords.includes('explain') ||
    messageWords.includes('definition')
  ) {
    contextualCalls.push(
      `📖 Retrieved information\n  ↳ Database: Knowledge base and documentation\n  ↳ Scope: Comprehensive topic coverage\n  ↳ Insights: Detailed explanations and context`
    )
  }

  // Cross-referencing for verification
  if (
    messageWords.includes('verify') ||
    messageWords.includes('confirm') ||
    messageWords.includes('check')
  ) {
    contextualCalls.push(
      `🔍 Cross-referenced sources\n  ↳ Verifying: Claims and information accuracy\n  ↳ Sources: Multiple authoritative references\n  ↳ Confidence: High reliability (95%+ agreement)`
    )
  }

  // Default fallback tool calls
  const fallbackCalls = [
    `🌐 Searched relevant information\n  ↳ Query: Context-specific search terms\n  ↳ Found: Multiple relevant resources\n  ↳ Sources: Verified and current information`,
    `📊 Analyzed available data\n  ↳ Processing: Available information and context\n  ↳ Scope: Comprehensive data review\n  ↳ Result: Structured analysis completed`,
    `📖 Retrieved supporting information\n  ↳ Database: Comprehensive knowledge base\n  ↳ Context: Topic-specific information\n  ↳ Insights: Relevant details and background`,
  ]

  // Combine contextual and fallback calls
  const allCalls = [...contextualCalls, ...fallbackCalls]

  // Determine number of tool calls (1-4 based on complexity)
  const numCalls = Math.min(Math.floor(userMessage.length / 40) + 1, 4)

  // Return first N calls without shuffling to maintain logical sequence
  return allCalls.slice(0, numCalls)
}

// Generate thinking content based on model type
function getSimulatedThinking(
  userMessage: string,
  model: string
): { thinking: string; toolCalls: string[]; thinkingTime: number } {
  const isThinking = isThinkingModel(model)
  const thinkingTime = getThinkingTime(userMessage)

  let thinking = ''
  let toolCalls: string[] = []

  if (isThinking) {
    // Thinking models show reasoning process
    thinking = `I need to carefully analyze the user's question: "${userMessage}". This requires a multi-step approach to provide a comprehensive and accurate response.\n\nLet me break this down:\n1. Understanding the core question and context\n2. Gathering relevant information through research\n3. Analyzing the data and drawing insights\n4. Structuring a helpful response\n\nThis appears to be a complex inquiry that would benefit from both reasoning and tool usage.`
  }

  // Both thinking and regular models can use tools
  const shouldUseTool = Math.random() > 0.3 // 70% chance
  if (shouldUseTool) {
    toolCalls = generateToolCalls(userMessage)
  }

  return { thinking, toolCalls, thinkingTime }
}

export async function POST(req: Request) {
  try {
    // Parse request body
    let requestData: any = null
    try {
      requestData = await req.json()
    } catch (error) {
      console.error('🧪 [Chat Simulator] Failed to parse JSON:', error)
      return NextResponse.json(
        { message: 'Invalid JSON in request body.' },
        { status: 400 }
      )
    }

    if (!requestData) {
      return NextResponse.json(
        { message: 'Request body is required.' },
        { status: 400 }
      )
    }

    console.log(
      '🧪 [Chat Simulator] Received request:',
      JSON.stringify(requestData, null, 2)
    )

    // Handle both useChat format and direct format
    let messages: any[] = []
    let model: string = 'gpt-4.1'
    let context: string | undefined

    if (requestData.messages) {
      // Standard useChat format
      messages = requestData.messages
      model = requestData.model || 'gpt-4.1'
      context = requestData.context
    } else {
      return NextResponse.json(
        { message: 'Messages array is required.' },
        { status: 400 }
      )
    }

    // Get the latest user message
    const userMessages = messages.filter(m => m.role === 'user')
    const latestUserMessage =
      userMessages[userMessages.length - 1]?.content || 'Hello'

    console.log('🧪 [Chat Simulator] Processing message:', latestUserMessage)

    // Generate thinking and tool calls based on model
    const { thinking, toolCalls, thinkingTime } = getSimulatedThinking(
      latestUserMessage,
      model
    )

    // Generate main response
    const responseContent = getSimulatedChatResponse(latestUserMessage, context)
    const responseWords = responseContent.split(' ')

    // Create mock model with thinking + response streaming
    const mockModel = new MockLanguageModelV1({
      doStream: async () => {
        const streamChunks: Array<{
          type: 'text-delta' | 'finish'
          textDelta?: string
          finishReason?: 'stop'
          usage?: { promptTokens: number; completionTokens: number }
        }> = []

        // Add thinking and tool calls if available
        if (thinking || toolCalls.length > 0) {
          // Start thinking section with metadata
          streamChunks.push({
            type: 'text-delta',
            textDelta: `<thinking time="${thinkingTime}">`,
          })

          // Add thinking content if this is a thinking model
          if (thinking) {
            const thinkingWords = thinking.split(' ')
            thinkingWords.forEach((word, i) => {
              streamChunks.push({
                type: 'text-delta',
                textDelta: i === 0 ? word : ' ' + word,
              })
            })

            if (toolCalls.length > 0) {
              streamChunks.push({ type: 'text-delta', textDelta: '\n\n' })
            }
          }

          // Add tool calls
          toolCalls.forEach((toolCall, index) => {
            if (index > 0) {
              streamChunks.push({ type: 'text-delta', textDelta: '\n\n' })
            }
            const toolWords = toolCall.split(' ')
            toolWords.forEach((word, i) => {
              streamChunks.push({
                type: 'text-delta',
                textDelta: i === 0 ? word : ' ' + word,
              })
            })
          })

          streamChunks.push({
            type: 'text-delta',
            textDelta: '</thinking>\n\n',
          })
        }

        // Add main response content
        responseWords.forEach((word, i) => {
          streamChunks.push({
            type: 'text-delta',
            textDelta: i === 0 ? word : ' ' + word,
          })
        })

        // Final finish chunk
        const totalTokens =
          responseWords.length +
          (thinking ? thinking.split(' ').length : 0) +
          toolCalls.reduce((sum, call) => sum + call.split(' ').length, 0)

        streamChunks.push({
          type: 'finish',
          finishReason: 'stop',
          usage: {
            promptTokens: 100,
            completionTokens: totalTokens,
          },
        })

        // Create readable stream with fast timing
        const stream = new ReadableStream({
          start(controller) {
            let index = 0
            const pushChunk = () => {
              if (index < streamChunks.length) {
                setTimeout(
                  () => {
                    console.log(
                      '🧪 [Chat Simulator] Enqueuing chunk',
                      index,
                      ':',
                      streamChunks[index]
                    )
                    controller.enqueue(streamChunks[index])
                    index++
                    pushChunk()
                  },
                  index === 0 ? STREAM_INITIAL_DELAY_MS : STREAM_CHUNK_DELAY_MS
                ) // streaming controlled by constants above
              } else {
                controller.close()
              }
            }
            pushChunk()
          },
        })

        return {
          stream,
          rawCall: { rawPrompt: latestUserMessage, rawSettings: {} },
        }
      },
    })

    const result = await streamText({
      model: mockModel,
      messages: [
        {
          role: 'system',
          content:
            'You are a helpful AI assistant in a drag tree chat interface. Provide comprehensive and well-structured responses using markdown formatting.',
        },
        ...messages,
      ],
    })

    const response = result.toDataStreamResponse()

    // Persist after stream sent
    const fullResponse = responseContent
    persistConversationTurn({
      conversationId: requestData.conversationId,
      userMessage: { role: 'USER', content: latestUserMessage },
      assistantMessage: { role: 'ASSISTANT', content: fullResponse },
    }).catch(err => console.error('Persist sim turn error', err))

    return response
  } catch (error) {
    console.error('Chat simulator API error:', error)
    return NextResponse.json({ message: 'Error' }, { status: 500 })
  }
}

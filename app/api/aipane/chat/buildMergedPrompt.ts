import { SYSTEM_PROMPT_CONFIG } from '@/constants/chat'
import { normaliseMessageContent } from '@/app/libs/ai-chat/content'

/**
 * Context message type for additional context in chat
 */
export type ContextMessage = {
  role: 'user' | 'assistant' | 'system'
  content: string
  metadata?: {
    type: string
    source?: string
    timestamp?: string
  }
}

/**
 * Regular chat message type
 */
export type ChatMessage = {
  role: 'user' | 'assistant' | 'system'
  content:
    | string
    | Array<{
        type: string
        text?: string
        image?: string
        [key: string]: any
      }>
  id?: string
  createdAt?: Date
}

/**
 * Options for building the merged prompt
 */
export type BuildMergedPromptOptions = {
  systemPrompt?: string
  contextMessages?: ContextMessage[]
  messages: ChatMessage[]
}

/**
 * Result of building the merged prompt
 */
export type MergedPromptResult = {
  systemPrompt: string
  messages: Array<{
    role: 'user' | 'assistant' | 'system'
    content: string
  }>
}

/**
 * Builds a merged prompt by combining system prompt, context messages, and regular messages
 * Handles content normalization and proper message ordering
 * @param options - The prompt building options
 * @returns The merged prompt with system message and normalized messages
 */
export function buildMergedPrompt(
  options: BuildMergedPromptOptions
): MergedPromptResult {
  const { systemPrompt, contextMessages = [], messages } = options

  // Use provided system prompt or fall back to default
  const finalSystemPrompt =
    systemPrompt || SYSTEM_PROMPT_CONFIG.DEFAULT_SYSTEM_PROMPT

  // Normalize regular messages
  const normalizedMessages = messages.map(msg => ({
    role: msg.role,
    content: normaliseMessageContent(msg.content),
  }))

  // Combine context messages with regular messages
  // Context messages come first to provide background information
  const allMessages = [
    ...contextMessages.map(msg => ({
      role: msg.role,
      content: msg.content,
    })),
    ...normalizedMessages,
  ]

  return {
    systemPrompt: finalSystemPrompt,
    messages: allMessages,
  }
}

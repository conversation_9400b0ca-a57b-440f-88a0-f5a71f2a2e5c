import { z } from 'zod'
import { SUPPORTED_MODELS, type SupportedModel } from '@/constants/chat'

/**
 * Zod schema for validating AI Pane Chat API requests
 * Provides type-safe validation with detailed error messages
 */

// Message content validation schema
const messageContentSchema = z.union([
  z.string(),
  z.array(
    z.union([
      z.object({
        type: z.literal('text'),
        text: z.string(),
      }),
      z.object({
        type: z.literal('image'),
        image: z.string(),
      }),
    ])
  ),
])

// Individual message schema
const messageSchema = z.object({
  role: z.enum(['user', 'assistant', 'system']),
  content: messageContentSchema,
  id: z.string().optional(),
  createdAt: z.date().optional(),
})

// Context message schema for additional context
const contextMessageSchema = z.object({
  role: z.enum(['user', 'assistant', 'system']),
  content: z.string(),
  metadata: z
    .object({
      type: z.string(),
      source: z.string().optional(),
      timestamp: z.string().optional(),
    })
    .optional(),
})

// Main request schema
export const AIPaneChatRequestSchema = z.object({
  messages: z.array(messageSchema).min(1, 'At least one message is required'),
  model: z
    .enum(SUPPORTED_MODELS as readonly [SupportedModel, ...SupportedModel[]])
    .default('gpt-4.1'),
  conversationId: z.string().optional(),
  contextMessages: z.array(contextMessageSchema).optional(),
  systemPrompt: z.string().optional(),
  userId: z.string().min(1, 'User ID is required'),
  temperature: z.number().min(0).max(2).optional(),
  maxTokens: z.number().positive().optional(),
  stream: z.boolean().default(true),
})

// Export the inferred type for use in other files
export type AIPaneChatRequest = z.infer<typeof AIPaneChatRequestSchema>

// Validation result types
export type ValidationSuccess<T> = {
  success: true
  data: T
}

export type ValidationError = {
  success: false
  error: {
    message: string
    issues: Array<{
      path: string[]
      message: string
      code: string
    }>
  }
}

export type ValidationResult<T> = ValidationSuccess<T> | ValidationError

/**
 * Validates an AI Pane Chat request using the Zod schema
 * @param requestBody - The raw request body to validate
 * @returns Validation result with typed data or detailed errors
 */
export function validateChatRequest(
  requestBody: unknown
): ValidationResult<AIPaneChatRequest> {
  const result = AIPaneChatRequestSchema.safeParse(requestBody)

  if (result.success) {
    return {
      success: true,
      data: result.data,
    }
  }

  // Transform Zod errors into a more user-friendly format
  const issues = result.error.issues.map(issue => ({
    path: issue.path.map(String),
    message: issue.message,
    code: issue.code,
  }))

  return {
    success: false,
    error: {
      message: `Validation failed: ${issues.map(i => i.message).join(', ')}`,
      issues,
    },
  }
}

/**
 * Type guard to check if a model is supported
 * @param model - The model to check
 * @returns True if the model is supported
 */
export function isSupportedModel(model: string): model is SupportedModel {
  return SUPPORTED_MODELS.includes(model as SupportedModel)
}

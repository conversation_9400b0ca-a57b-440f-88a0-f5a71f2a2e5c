import { Ai<PERSON>hat<PERSON>ogger } from '@/app/server-actions/ai-chat/logging'

// Import builders from co-located _lib
import { validateChatRequestPipeline } from './_lib/validation-builder'
import { normalizeMessages, buildModelMessages } from './_lib/message-builder'
import { 
  createChatDataStreamResponse, 
  extractRequestData,
  type ProcessedRequestData 
} from './_lib/route-builder'

export const maxDuration = 60

// Initialize structured logger
const chatLogger = new AiChatLogger('aipane-chat-route')

export async function POST(req: Request) {
  try {
    // Validate request using pipeline
    const validationResult = await validateChatRequestPipeline(req)
    if (!validationResult.success) {
      return validationResult.response
    }

    // Extract and validate request data
    const {
      userId,
      conversationId,
      model,
      context,
      settings,
      rawMessages,
    } = extractRequestData(validationResult)

    chatLogger.info('Received chat request', { userId })

    // Normalize message content format
    const messages = normalizeMessages(rawMessages)

    chatLogger.info('Processing chat request', { userId })

    // Build messages for AI model with context merging
    const modelMessages = await buildModelMessages(conversationId, messages, context)

    // Prepare processed data for streaming
    const processedData: ProcessedRequestData = {
      userId,
      conversationId,
      model,
      settings,
      context,
      messages,
      modelMessages,
      logger: chatLogger,
    }

    // Create and return the data stream response
    return createChatDataStreamResponse(processedData)
  } catch (error) {
    chatLogger.error('Chat API error', error)
    return new Response('Error generating response', { status: 500 })
  }
}

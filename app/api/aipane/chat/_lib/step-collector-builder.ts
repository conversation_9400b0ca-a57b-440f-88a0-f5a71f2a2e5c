/**
 * Step collector and streaming utilities
 * Handles execution step collection and real-time streaming logic
 */

import { createExecutionStepCollector } from '@/app/server-actions/ai-chat'
import type { SearchProgressCallback } from '@/app/api/dragtree/shared/search-tools'
import type { SearchMetadata } from '@/app/api/dragtree/shared/brave-search'
import { TOOL_NAMES } from '@/constants/chat'
import { AiChatLogger } from '@/app/server-actions/ai-chat/logging'

/**
 * Create search progress callback for execution step tracking
 */
export function createSearchProgressCallback(
  stepCollector: ReturnType<typeof createExecutionStepCollector>,
  logger: AiChatLogger,
  userId: string
): SearchProgressCallback {
  return (status) => {
    logger.debug('Search progress update', { userId })

    // Add search execution steps to collector for persistence
    if (status.type === 'searching') {
      stepCollector.addToolCall(TOOL_NAMES.WEB_SEARCH, {
        query: status.query,
      })
    } else if (status.type === 'completed') {
      stepCollector.addToolResult(TOOL_NAMES.WEB_SEARCH, {
        query: status.query,
        resultCount: status.resultCount,
      })
    } else if (status.type === 'error') {
      stepCollector.addToolResult(TOOL_NAMES.WEB_SEARCH, {
        error: status.error,
      })
    }
  }
}

/**
 * Create chunk handler for streaming tool calls and results
 */
export function createChunkHandler(
  stepCollector: ReturnType<typeof createExecutionStepCollector>,
  dataStream: any
) {
  return async (chunk: any) => {
    // Capture tool calls and add them to the execution step collector
    if (chunk.chunk.type === 'tool-call') {
      stepCollector.addToolCall(chunk.chunk.toolName, chunk.chunk.args)

      // Stream the step data to the client
      dataStream.writeData({
        type: 'execution-step',
        step: {
          type: 'TOOL_CALL',
          toolName: chunk.chunk.toolName,
          args: chunk.chunk.args,
          toolCallId: chunk.chunk.toolCallId,
          timestamp: Date.now(),
        },
      })
    }

    if (chunk.chunk.type === 'tool-result') {
      stepCollector.addToolResult(
        chunk.chunk.toolName,
        chunk.chunk.result
      )

      // Stream the step result to the client (optional - we're filtering these out in UI)
      dataStream.writeData({
        type: 'execution-step',
        step: {
          type: 'TOOL_RESULT',
          toolName: chunk.chunk.toolName,
          result: chunk.chunk.result,
          toolCallId: chunk.chunk.toolCallId,
          timestamp: Date.now(),
        },
      })
    }
  }
}

/**
 * Add reasoning summary if there were execution steps
 */
export function addReasoningSummaryIfNeeded(
  stepCollector: ReturnType<typeof createExecutionStepCollector>,
  searchMetadata: SearchMetadata[]
): void {
  if (stepCollector.getStepCount() > 0) {
    const summary = `Generated response using ${searchMetadata.length} web search(es) and ${stepCollector.getStepCount()} execution step(s)`
    stepCollector.addReasoningSummary(summary)
  }
}
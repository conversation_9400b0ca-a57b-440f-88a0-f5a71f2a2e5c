/**
 * Route building utilities for chat API
 * Handles the main request processing logic
 */

import { createDataStreamResponse, streamText } from 'ai'
import { createExecutionStepCollector } from '@/app/server-actions/ai-chat'
import { createSearchToolAdapter } from '../tools'
import type { SearchMetadata } from '@/app/api/dragtree/shared/brave-search'
import type { AiChatLogger } from '@/app/server-actions/ai-chat/logging'
import type { ChatMessage } from '@/types/chat'

import {
  createSearchProgressCallback,
  createChunkHandler,
  addReasoningSummaryIfNeeded,
} from './step-collector-builder'
import {
  persistContextIfNeeded,
  persistConversationTurnWithRetry,
  logAIUsage,
} from './persistence-builder'
import {
  createStreamConfig,
  initializeStream,
  handleStreamFinish,
  createPostProcessingHandler,
  createStreamErrorHandler,
  type PostProcessingData,
} from './stream-builder'

/**
 * Processed request data ready for streaming
 */
export type ProcessedRequestData = {
  userId: string
  conversationId: string
  model: string
  settings: Record<string, any>
  context?: string
  messages: ChatMessage[]
  modelMessages: { role: 'system' | 'user' | 'assistant'; content: string }[]
  logger: AiChatLogger
}

/**
 * Create the main data stream response for chat
 */
export function createChatDataStreamResponse(data: ProcessedRequestData) {
  const {
    userId,
    conversationId,
    model,
    settings,
    context,
    messages,
    modelMessages,
    logger,
  } = data

  // Initialize execution step collector and search metadata
  const stepCollector = createExecutionStepCollector()
  const searchMetadata: SearchMetadata[] = []

  // Create search progress callback for execution step tracking
  const searchProgressCallback = createSearchProgressCallback(
    stepCollector,
    logger,
    userId
  )

  return createDataStreamResponse({
    execute: dataStream => {
      // Initialize streaming
      initializeStream(dataStream, conversationId)

      // Prepare post-processing data
      const postProcessingData: PostProcessingData = {
        conversationId,
        userId,
        messages,
        context,
        model,
        settings,
        stepCollector,
        searchMetadata,
        finalResponse: '', // Will be set in onFinish
        result: null, // Will be set in onFinish
        logger,
      }

      // Create the onFinish handler
      const onFinishHandler = createPostProcessingHandler(
        postProcessingData,
        persistContextIfNeeded,
        persistConversationTurnWithRetry,
        logAIUsage,
        addReasoningSummaryIfNeeded
      )

      // Wrap the onFinish handler to include stream completion notification
      const wrappedOnFinish = async (result: any) => {
        // Notify client that streaming is complete
        handleStreamFinish(dataStream)
        
        // Execute post-processing
        await onFinishHandler(result)
      }

      // Create stream configuration
      const streamConfig = createStreamConfig({
        model,
        messages: modelMessages,
        tools: createSearchToolAdapter(searchMetadata, searchProgressCallback),
        onChunk: createChunkHandler(stepCollector, dataStream),
        onFinish: wrappedOnFinish,
      })

      // Start streaming
      const result = streamText(streamConfig)

      // Merge the streamText result into the data stream
      result.mergeIntoDataStream(dataStream)
    },
    onError: createStreamErrorHandler(logger, userId),
  })
}

/**
 * Extract and validate required data from validation result
 */
export function extractRequestData(validationResult: any) {
  const { userId, requestData } = validationResult
  const { 
    messages: rawMessages, 
    model, 
    context, 
    settings, 
    conversationId 
  } = requestData

  return {
    userId,
    conversationId: conversationId!,
    model,
    context,
    settings,
    rawMessages,
  }
}
/**
 * Unit tests for message-builder utilities
 * Tests message normalization and content extraction functions
 */

import { normalizeMessages, extractLatestUserMessage } from '../message-builder'
import type { AIPaneChatRequest, MessageContent } from '@/types/chat'

// Mock the external dependencies
jest.mock('@/app/libs/ai-chat/content', () => ({
  normaliseMessageContent: jest.fn((content) => {
    if (typeof content === 'string') return content
    if (Array.isArray(content)) {
      return content.map(part => part.text).join('')
    }
    return String(content)
  })
}))

describe('message-builder', () => {
  describe('normalizeMessages', () => {
    it('should normalize string content messages', () => {
      const rawMessages: AIPaneChatRequest['messages'] = [
        {
          role: 'user',
          content: 'Hello world'
        },
        {
          role: 'assistant',
          content: 'Hi there!'
        }
      ]

      const result = normalizeMessages(rawMessages)

      expect(result).toEqual([
        {
          role: 'user',
          content: 'Hello world'
        },
        {
          role: 'assistant',
          content: 'Hi there!'
        }
      ])
    })

    it('should normalize multimodal content messages', () => {
      const rawMessages: AIPaneChatRequest['messages'] = [
        {
          role: 'user',
          content: [
            { type: 'text', text: 'Hello ' },
            { type: 'text', text: 'world' }
          ]
        }
      ]

      const result = normalizeMessages(rawMessages)

      expect(result).toEqual([
        {
          role: 'user',
          content: 'Hello world'
        }
      ])
    })

    it('should handle empty messages array', () => {
      const rawMessages: AIPaneChatRequest['messages'] = []
      const result = normalizeMessages(rawMessages)
      expect(result).toEqual([])
    })

    it('should preserve role and add normalized content', () => {
      const rawMessages: AIPaneChatRequest['messages'] = [
        {
          role: 'system',
          content: 'You are a helpful assistant'
        }
      ]

      const result = normalizeMessages(rawMessages)

      expect(result[0].role).toBe('system')
      expect(result[0].content).toBe('You are a helpful assistant')
    })
  })

  describe('extractLatestUserMessage', () => {
    it('should extract the latest user message content', () => {
      const messages = [
        { role: 'user' as const, content: 'First message' },
        { role: 'assistant' as const, content: 'Response' },
        { role: 'user' as const, content: 'Latest message' }
      ]

      const result = extractLatestUserMessage(messages)
      expect(result).toBe('Latest message')
    })

    it('should return empty string if no user messages exist', () => {
      const messages = [
        { role: 'assistant' as const, content: 'Response only' },
        { role: 'system' as const, content: 'System message' }
      ]

      const result = extractLatestUserMessage(messages)
      expect(result).toBe('')
    })

    it('should return empty string for empty messages array', () => {
      const messages: any[] = []
      const result = extractLatestUserMessage(messages)
      expect(result).toBe('')
    })

    it('should skip non-user messages when finding latest', () => {
      const messages = [
        { role: 'user' as const, content: 'User message' },
        { role: 'assistant' as const, content: 'Assistant response' },
        { role: 'system' as const, content: 'System prompt' }
      ]

      const result = extractLatestUserMessage(messages)
      expect(result).toBe('User message')
    })

    it('should handle multimodal content in user messages', () => {
      const messages = [
        { 
          role: 'user' as const, 
          content: [
            { type: 'text', text: 'Hello ' },
            { type: 'text', text: 'world' }
          ]
        }
      ]

      const result = extractLatestUserMessage(messages)
      expect(result).toBe('Hello world')
    })
  })
})
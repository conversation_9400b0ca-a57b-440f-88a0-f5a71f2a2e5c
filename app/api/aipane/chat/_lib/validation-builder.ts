/**
 * Request validation utilities for chat API
 * Handles authentication, rate limiting, and request body validation
 */

import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/authOptions'
import { isRateLimited, getRetryAfterSeconds } from '@/app/libs/rateLimiter'
import { standardErrors } from '../../shared/errors'
import type { AIPaneChatRequest } from '@/types/chat'
import { RATE_LIMIT_CONFIG, MODEL_CONFIG } from '@/constants/chat'
import { createLogger } from '@/libs/debug'

const logger = createLogger('CHAT_VALIDATION')

export type ValidatedChatRequest = {
  messages: AIPaneChatRequest['messages']
  model: string
  settings: Record<string, any>
  conversationId: string
  context?: string
  contextEntityType?: string
  contextEntityId?: string
}

export type ValidationResult = 
  | { success: true; userId: string; requestData: ValidatedChatRequest }
  | { success: false; response: Response }

/**
 * Validate user authentication
 */
export async function validateAuthentication(): Promise<
  | { success: true; userId: string }
  | { success: false; response: Response }
> {
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    return { success: false, response: standardErrors.unauthorized() }
  }
  return { success: true, userId: session.user.id }
}

/**
 * Check rate limiting for user
 */
export function validateRateLimit(userId: string): 
  | { success: true }
  | { success: false; response: Response }
{
  const rateLimitKey = `${userId}:aipane:chat`
  if (isRateLimited(rateLimitKey, RATE_LIMIT_CONFIG.WINDOW_MS)) {
    const retryAfter = getRetryAfterSeconds(
      rateLimitKey,
      RATE_LIMIT_CONFIG.WINDOW_MS
    )
    return {
      success: false,
      response: standardErrors.rateLimited(
        retryAfter,
        RATE_LIMIT_CONFIG.MAX_REQUESTS.toString()
      )
    }
  }
  return { success: true }
}

/**
 * Parse and validate request body
 */
export async function validateRequestBody(req: Request): Promise<
  | { success: true; requestData: AIPaneChatRequest }
  | { success: false; response: Response }
> {
  let requestData: AIPaneChatRequest

  try {
    const rawData = await req.json()
    requestData = rawData as AIPaneChatRequest
  } catch (error) {
    logger.error('Failed to parse JSON:', error)
    return { success: false, response: standardErrors.invalidJson() }
  }

  if (!requestData) {
    return { success: false, response: standardErrors.missingBody() }
  }

  return { success: true, requestData }
}

/**
 * Validate chat request data
 */
export function validateChatRequest(requestData: AIPaneChatRequest): 
  | { success: true; normalizedData: ValidatedChatRequest }
  | { success: false; response: Response }
{
  const { messages: rawMessages, conversationId } = requestData

  // Validate raw messages
  if (!rawMessages || !Array.isArray(rawMessages)) {
    return { success: false, response: standardErrors.invalidMessages() }
  }

  // Validate messages array
  if (rawMessages.length === 0) {
    return {
      success: false,
      response: standardErrors.invalidMessages(
        'Messages array must contain at least one message'
      )
    }
  }

  // ConversationId is mandatory now to avoid duplicate rows
  if (!conversationId || !conversationId.startsWith('thread_')) {
    return {
      success: false,
      response: standardErrors.invalidIdFormat('conversationId', 'thread_*')
    }
  }

  return {
    success: true,
    normalizedData: {
      messages: rawMessages,
      model: requestData.model || MODEL_CONFIG.DEFAULT_MODEL,
      settings: requestData.settings || {},
      conversationId: conversationId,
      context: requestData.context,
      contextEntityType: requestData.contextEntityType,
      contextEntityId: requestData.contextEntityId,
    }
  }
}

/**
 * Complete request validation pipeline
 */
export async function validateChatRequestPipeline(req: Request): Promise<ValidationResult> {
  // 1. Validate authentication
  const authResult = await validateAuthentication()
  if (!authResult.success) {
    return authResult
  }

  // 2. Check rate limiting
  const rateLimitResult = validateRateLimit(authResult.userId)
  if (!rateLimitResult.success) {
    return rateLimitResult
  }

  // 3. Parse request body
  const bodyResult = await validateRequestBody(req)
  if (!bodyResult.success) {
    return bodyResult
  }

  // 4. Validate chat request
  const chatResult = validateChatRequest(bodyResult.requestData)
  if (!chatResult.success) {
    return chatResult
  }

  return {
    success: true,
    userId: authResult.userId,
    requestData: chatResult.normalizedData,
  }
}
/**
 * Persistence utilities for chat API
 * Handles atomic conversation turn persistence with retry logic
 */

import {
  persistConversationTurn,
  addMessageToConversation,
  createExecutionStepCollector,
} from '@/app/server-actions/ai-chat'
import { createAIUsage } from '@/app/server-actions/log_ai_usage'
import { AIUsageType } from '@prisma/client'
import type { SearchMetadata } from '@/app/api/dragtree/shared/brave-search'
import { RETRY_CONFIG } from '@/constants/chat'
import { AiChatLogger } from '@/app/server-actions/ai-chat/logging'
import type { ChatMessage } from '@/types/chat'
import { extractTextFromContent } from '@/types/chat'

export type PersistenceOptions = {
  conversationId: string
  userId: string
  messages: ChatMessage[]
  context?: string
  model: string
  settings: Record<string, any>
  stepCollector: ReturnType<typeof createExecutionStepCollector>
  searchMetadata: SearchMetadata[]
  finalResponse: string
  result: any // AI SDK result object
  logger: <PERSON><PERSON>hatLogger
}

/**
 * Persist context message for first conversation turn
 */
export async function persistContextIfNeeded(
  conversationId: string,
  context: string | undefined,
  messages: ChatMessage[],
  logger: AiChatLogger,
  userId: string
): Promise<void> {
  const isFirstTurn = messages.length === 1 // only the new user msg

  if (isFirstTurn && context && context.trim().length > 0) {
    try {
      await addMessageToConversation(conversationId, {
        role: 'SYSTEM',
        content: `CONTEXT:\n${context}`,
      })
      logger.info('Persisted context message', { userId })
    } catch (ctxErr) {
      logger.warn('Failed to persist context message', { userId })
    }
  }
}

/**
 * Persist conversation turn with retry logic
 */
export async function persistConversationTurnWithRetry(
  options: PersistenceOptions
): Promise<void> {
  const {
    conversationId,
    userId,
    messages,
    stepCollector,
    finalResponse,
    logger,
  } = options

  // Get the latest user message content
  const userMessages = messages.filter(m => m.role === 'user')
  const latestUser = userMessages[userMessages.length - 1]
  const latestUserMessage = latestUser ? extractTextFromContent(latestUser.content) : ''

  const MAX_RETRIES = RETRY_CONFIG.MAX_RETRIES
  let attempt = 0
  let persistResult: Awaited<ReturnType<typeof persistConversationTurn>> | null = null

  while (attempt < MAX_RETRIES) {
    attempt++
    
    persistResult = await persistConversationTurn({
      conversationId,
      userMessage: {
        role: 'USER',
        content: latestUserMessage,
      },
      assistantMessage: {
        role: 'ASSISTANT',
        content: finalResponse,
        steps: stepCollector.getSteps(),
      },
    })

    if (persistResult.success) break

    logger.warn('Persistence attempt failed', { userId })
    
    // Back-off delay using config constant
    await new Promise(res =>
      setTimeout(res, RETRY_CONFIG.BACKOFF_MS * attempt)
    )
  }

  if (!persistResult?.success) {
    // CRITICAL: Log this failure – user saw response but it wasn't saved
    logger.error(
      'FATAL: Failed to save conversation turn after all retries',
      { userId }
    )
  } else {
    logger.info('Successfully persisted conversation turn', { userId })
  }
}

/**
 * Log AI usage for monitoring and analytics
 */
export async function logAIUsage(options: PersistenceOptions): Promise<void> {
  const {
    userId,
    conversationId,
    model,
    messages,
    context,
    settings,
    stepCollector,
    searchMetadata,
    finalResponse,
    result,
    logger,
  } = options

  try {
    // Get the latest user message content
    const userMessages = messages.filter(m => m.role === 'user')
    const latestUser = userMessages[userMessages.length - 1]
    const latestUserMessage = latestUser ? extractTextFromContent(latestUser.content) : ''

    // Build model messages for logging (simplified version)
    const modelMessages = [
      ...messages.map(m => ({ role: m.role, content: extractTextFromContent(m.content) })),
      { role: 'assistant', content: finalResponse },
    ]

    await createAIUsage({
      userId,
      entityType: 'aipane',
      entityId: conversationId,
      aiProvider: 'azure_openai',
      modelName: model,
      usageType: AIUsageType.CHAT,
      inputPrompt: latestUserMessage,
      messages: modelMessages,
      metadata: {
        endpoint: '/api/aipane/chat',
        promptTokens: result.usage?.promptTokens || 0,
        completionTokens: result.usage?.completionTokens || 0,
        totalTokens: result.usage?.totalTokens || 0,
        finishReason: result.finishReason,
        hasContext: !!context,
        messageCount: messages.length,
        conversationId,
        executionSteps: stepCollector.getStepCount(),
        hasThinking: stepCollector.getSummary().stepsByType.THOUGHT > 0,
        hasToolCalls: stepCollector.getSummary().stepsByType.TOOL_CALL > 0,
        searchResults: searchMetadata.length,
      },
      config: settings,
    })
  } catch (error) {
    logger.error('Failed to log AI usage', error, { userId })
  }
}
/**
 * Message building utilities for chat API
 * Handles message normalization, context merging, and model message preparation
 */

import { buildContextMessages } from '@/app/libs/llmContext'
import { normaliseMessageContent } from '@/app/libs/ai-chat/content'
import type { AIPaneChatRequest, ChatMessage } from '@/types/chat'
import { extractTextFromContent } from '@/types/chat'

/**
 * Normalize message content from Vercel AI SDK format to string
 */
export function normalizeMessages(rawMessages: AIPaneChatRequest['messages']): ChatMessage[] {
  return rawMessages.map(m => ({
    ...m,
    content: normaliseMessageContent((m as any).content),
  }))
}

/**
 * Build messages for AI model with context merging
 */
export async function buildModelMessages(
  conversationId: string,
  messages: ChatMessage[],
  context?: string
): Promise<{ role: 'system' | 'user' | 'assistant'; content: string }[]> {
  // Get the latest user message
  const latestUser = messages[messages.length - 1]
  
  // Build the base message list (system prompt + history + latest user msg)
  const { messages: baseMessages } = await buildContextMessages(
    conversationId,
    {
      role: 'user',
      content: extractTextFromContent(latestUser.content),
    },
    100_000
  )

  // ──────────────────────────────────────────────────────────
  // Combine system prompt + context into ONE message to reduce tokens.
  // 1. Start with default system prompt (always baseMessages[0]).
  // 2. Determine context text – from current request or from the first
  //    persisted system message that starts with "CONTEXT:".
  // 3. Build a merged prompt:
  //    "You are …\n\nWe did some quick researches, below are the findings:\n<ctx>"
  // 4. Remove the standalone CONTEXT system message (if any).
  // ──────────────────────────────────────────────────────────

  const defaultPrompt = baseMessages[0]

  // Try to get context from request first (brand-new convo)
  let ctxText: string | undefined =
    context && context.trim().length > 0 ? context : undefined

  // Otherwise look for a persisted CONTEXT message
  if (!ctxText && baseMessages.length > 1) {
    const maybeCtx = baseMessages[1]
    if (
      maybeCtx.role === 'system' &&
      maybeCtx.content.startsWith('CONTEXT:\n')
    ) {
      ctxText = maybeCtx.content.replace(/^CONTEXT:\n/, '')
    }
  }

  let mergedSystemContent = defaultPrompt.content
  if (ctxText) {
    mergedSystemContent += `\n\nWe did some quick researches, below are the findings:\n${ctxText}`
  }

  // Build final message array: merged system + rest without duplicate ctx msg
  const modelMessages = [
    {
      role: 'system' as const,
      content: mergedSystemContent,
    },
    // Skip the old context system msg if we merged it (index 1)
    ...baseMessages.slice(ctxText ? 2 : 1),
  ]

  return modelMessages
}

/**
 * Extract latest user message content
 */
export function extractLatestUserMessage(messages: ChatMessage[]): string {
  const userMessages = messages.filter(m => m.role === 'user')
  const latestUser = userMessages[userMessages.length - 1]
  return latestUser ? extractTextFromContent(latestUser.content) : ''
}
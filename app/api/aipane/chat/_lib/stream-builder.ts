/**
 * Stream building utilities for chat API
 * Handles stream configuration, initialization, and post-processing
 */

import { streamText } from 'ai'
import { azure } from '@ai-sdk/azure'
import type { SearchMetadata } from '@/app/api/dragtree/shared/brave-search'
import type { ChatMessage } from '@/types/chat'
import { MODEL_CONFIG } from '@/constants/chat'
import type { Ai<PERSON>hatLogger } from '@/app/server-actions/ai-chat/logging'

/**
 * Configuration for stream setup
 */
export type StreamConfig = {
  model: string
  messages: { role: 'system' | 'user' | 'assistant'; content: string }[]
  tools: any
  onChunk: (chunk: any) => void
  onFinish: (result: any) => Promise<void>
}

/**
 * Create streamText configuration object
 */
export function createStreamConfig({
  model,
  messages,
  tools,
  onChunk,
  onFinish,
}: StreamConfig) {
  return {
    model: azure(model),
    messages,
    tools,
    maxSteps: MODEL_CONFIG.MAX_STEPS,
    temperature: MODEL_CONFIG.TEMPERATURE,
    maxTokens: MODEL_CONFIG.MAX_TOKENS,
    onChunk,
    onFinish,
  }
}

/**
 * Initialize stream with start event
 */
export function initializeStream(
  dataStream: any,
  conversationId: string
): void {
  dataStream.writeData({
    type: 'stream-start',
    conversationId,
    timestamp: Date.now(),
  })
}

/**
 * Handle stream completion event
 */
export function handleStreamFinish(
  dataStream: any
): void {
  dataStream.writeData({
    type: 'stream-finish',
    timestamp: Date.now(),
  })
}

/**
 * Post-processing data for persistence
 */
export type PostProcessingData = {
  conversationId: string
  userId: string
  messages: ChatMessage[]
  context?: string
  model: string
  settings: Record<string, any>
  stepCollector: any
  searchMetadata: SearchMetadata[]
  finalResponse: string
  result: any
  logger: AiChatLogger
}

/**
 * Create post-processing handler for stream finish
 */
export function createPostProcessingHandler(
  data: PostProcessingData,
  persistContextIfNeeded: (
    conversationId: string,
    context: string | undefined,
    messages: ChatMessage[],
    logger: AiChatLogger,
    userId: string
  ) => Promise<void>,
  persistConversationTurnWithRetry: (data: any) => Promise<void>,
  logAIUsage: (data: any) => Promise<void>,
  addReasoningSummaryIfNeeded: (stepCollector: any, searchMetadata: SearchMetadata[]) => void
) {
  return async (result: any) => {
    const { 
      conversationId, userId, messages, context, model, settings,
      stepCollector, searchMetadata, logger
    } = data

    try {
      logger.info('Stream finished, starting atomic persistence', { userId })

      // Use the clean content directly from the result
      const finalResponse = result.text

      // Add reasoning summary if there were any tool calls or steps
      addReasoningSummaryIfNeeded(stepCollector, searchMetadata)

      // Log execution summary
      const summary = stepCollector.getSummary()
      logger.info('Execution summary', { userId })

      // Persist context message for first conversation turn
      await persistContextIfNeeded(
        conversationId,
        context,
        messages,
        logger,
        userId
      )

      // Persist conversation turn with retry logic
      await persistConversationTurnWithRetry({
        conversationId,
        userId,
        messages,
        context,
        model,
        settings,
        stepCollector,
        searchMetadata,
        finalResponse,
        result,
        logger,
      })

      // Log AI usage for monitoring
      await logAIUsage({
        conversationId,
        userId,
        messages,
        context,
        model,
        settings,
        stepCollector,
        searchMetadata,
        finalResponse,
        result,
        logger,
      })
    } catch (error) {
      // CRITICAL: Log this failure - the user saw a response but it wasn't saved
      logger.error(
        'FATAL: Failed to process conversation turn',
        error,
        { userId }
      )
    }
  }
}

/**
 * Create error handler for stream
 */
export function createStreamErrorHandler(
  logger: AiChatLogger,
  userId: string
) {
  return (error: any) => {
    logger.error('Stream error occurred', error, { userId })
    return error instanceof Error ? error.message : String(error)
  }
}
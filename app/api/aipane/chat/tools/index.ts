/**
 * Central tool adapters for AI chat API
 * Provides a unified interface for all tools used in the chat system
 */

import { buildSearchTools, type SearchProgressCallback } from '@/app/api/dragtree/shared/search-tools'
import type { SearchMetadata } from '@/app/api/dragtree/shared/brave-search'
import { TOOL_NAMES } from '@/constants/chat'

/**
 * Generic tool adapter interface
 */
export interface ToolAdapter {
  name: string
  execute: (args: any) => Promise<any>
  metadata?: Record<string, any>
}

/**
 * Tool configuration for chat API
 */
export interface ToolConfig {
  searchEnabled: boolean
  searchCallback?: SearchProgressCallback
  customTools?: ToolAdapter[]
}

/**
 * Build all tools for the AI chat system
 */
export function buildChatTools(
  searchMetadata: SearchMetadata[],
  config: ToolConfig
): Record<string, any> {
  const tools: Record<string, any> = {}

  // Add search tools if enabled
  if (config.searchEnabled && config.searchCallback) {
    const searchTools = buildSearchTools(searchMetadata, config.searchCallback)
    Object.assign(tools, searchTools)
  }

  // Add custom tools
  if (config.customTools) {
    for (const tool of config.customTools) {
      tools[tool.name] = {
        execute: tool.execute,
        ...tool.metadata,
      }
    }
  }

  return tools
}

/**
 * Get available tool names
 */
export function getAvailableTools(config: ToolConfig): string[] {
  const tools: string[] = []

  if (config.searchEnabled) {
    tools.push(TOOL_NAMES.WEB_SEARCH)
  }

  if (config.customTools) {
    tools.push(...config.customTools.map(tool => tool.name))
  }

  return tools
}

/**
 * Create search tool adapter
 */
export function createSearchToolAdapter(
  searchMetadata: SearchMetadata[],
  progressCallback: SearchProgressCallback
): Record<string, any> {
  return buildSearchTools(searchMetadata, progressCallback)
}

/**
 * Tool execution result
 */
export interface ToolExecutionResult {
  success: boolean
  result?: any
  error?: string
  metadata?: Record<string, any>
}

/**
 * Safe tool execution wrapper
 */
export async function executeToolSafely(
  tool: ToolAdapter,
  args: any
): Promise<ToolExecutionResult> {
  try {
    const result = await tool.execute(args)
    return {
      success: true,
      result,
      metadata: tool.metadata,
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
      metadata: tool.metadata,
    }
  }
}
'use client'

import React, { useState, createContext, useContext } from 'react'
import {
  MessagePrimitive,
  useMessage,
} from '@assistant-ui/react'
import { ReasoningTimeline } from '../ReasoningTimeline'
import {
  <PERSON>C<PERSON>,
  FiChevronDown,
  <PERSON>Chevron<PERSON>p,
  <PERSON>S<PERSON>ch,
  <PERSON>Loader,
} from 'react-icons/fi'
import toast from 'react-hot-toast'
import { createLogger } from '@/libs/debug'
import type { UIChatMessage } from '@/types/chat'

const logger = createLogger('CUSTOM_MESSAGE')

// Context to pass initial messages data to custom message components
const InitialMessagesContext = createContext<ExtendedMessage[]>([])
export const useInitialMessages = () => useContext(InitialMessagesContext)

/**
 * Extended message type compatible with ChatTabContent
 * @deprecated Use UIChatMessage from @/types/chat instead
 */
export type ExtendedMessage = UIChatMessage & {
  createdAt: string // Legacy string format for backward compatibility
  cleanContent?: string // Legacy property
}

/**
 * Props for CustomMessage component
 */
export type CustomMessageProps = {
  messageId?: string
  conversationId?: string
  liveSteps?: any[]
  isStreamingSteps?: boolean
}

/**
 * Tool call component for web search
 */
const WebSearchToolCallPart: React.FC<any> = ({ args, status }) => {
  const query = args?.query || ''
  const isRunning = status?.type === 'running'
  const resultCount = status?.result?.resultCount || status?.resultCount
  return (
    <div className="flex items-center gap-1 text-xs text-gray-500 my-1">
      <FiSearch className="w-3 h-3" />
      {isRunning ? (
        <>
          <FiLoader className="w-3 h-3 animate-spin" /> Searching:{' '}
          <span className="font-mono">{query}</span>
        </>
      ) : (
        <>
          Found <span className="font-semibold">{resultCount ?? '?'}</span>{' '}
          results for <span className="font-mono">{query}</span>
        </>
      )}
    </div>
  )
}

/**
 * Custom message component that can render tool calls and reasoning
 */
export const CustomMessage: React.FC<CustomMessageProps> = ({
  messageId,
  conversationId,
  liveSteps = [],
  isStreamingSteps = false,
}) => {
  // Use selector that returns the store object itself to keep snapshot stable
  const content = useMessage(m => m.content)
  const role = useMessage(m => m.role)
  const status = useMessage(m => m.status)
  const id = useMessage(m => m.id)

  // Get initial messages from context to find step data
  const initialMessages = useInitialMessages()

  // Find step data for this message from initial messages or use live steps if streaming
  const stepData = React.useMemo(() => {
    const messageData = initialMessages.find(
      msg => msg.id === (id || messageId)
    )
    const isCurrentMessage =
      status?.type === 'running' ||
      (messageData?.isStreaming && isStreamingSteps)

    // Use live steps for the currently streaming message, otherwise use stored steps
    if (isCurrentMessage && isStreamingSteps && liveSteps.length > 0) {
      return {
        stepCount: liveSteps.length,
        steps: liveSteps,
        isStreaming: true,
      }
    }

    return {
      stepCount: messageData?.stepCount || 0,
      steps: messageData?.steps || [],
      isStreaming: messageData?.isStreaming || false,
    }
  }, [initialMessages, id, messageId, status, isStreamingSteps, liveSteps])

  const message = React.useMemo(
    () => ({
      content,
      role,
      status,
      id: id || messageId || '',
    }),
    [content, role, status, id, messageId]
  )

  // Memoise tool components mapping to avoid new object each render
  const toolComponents = React.useMemo(
    () => ({
      Fallback: WebSearchToolCallPart,
      by_name: {
        web_search: WebSearchToolCallPart,
      },
    }),
    []
  )

  // Determine if message is long (heuristic by character count)
  const LONG_THRESHOLD = 800
  const charCount = (() => {
    if (!message.content || typeof message.content !== 'string') return 0
    return (message.content as string).length
  })()

  const hasTextContent = (() => {
    if (!message.content) return false
    if (typeof message.content === 'string')
      return (message.content as string).trim().length > 0
    // If content is array of parts, check if any text part exists
    if (Array.isArray(message.content)) {
      return message.content.some((p: any) => {
        if (!p || p.type !== 'text') return false
        const txt = p.text as string | undefined
        return typeof txt === 'string' && (txt as string).trim().length > 0
      })
    }
    return false
  })()
  const isLong = charCount > LONG_THRESHOLD
  const [collapsed, setCollapsed] = useState<boolean>(isLong)

  // Copy message content to clipboard
  const handleCopy = async () => {
    try {
      const textToCopy =
        typeof message.content === 'string'
          ? (message.content as string)
          : Array.isArray(message.content)
            ? (message.content as any[])
                .map(part => {
                  if (typeof part === 'string') return part
                  // Fallback for structured parts
                  if ('text' in part) return part.text
                  return ''
                })
                .join('')
            : JSON.stringify(message.content)

      await navigator.clipboard.writeText(textToCopy)
      toast.success('Message copied')
    } catch (e) {
      toast.error('Failed to copy')
    }
  }

  // Check if this message has step count (indicating reasoning/tool usage)
  const hasSteps = message.role === 'assistant' && stepData.stepCount > 0

  // Debug logging for step data
  if (message.role === 'assistant') {
    logger.debug('Message step data:', {
      messageId: message.id,
      stepCount: stepData.stepCount,
      hasSteps,
      initialMessagesCount: initialMessages.length,
    })
  }

  const bubbleBaseClasses =
    'relative inline-block rounded-lg max-w-[80%] text-sm leading-relaxed'
  const bubblePadding = 'px-3 py-2'

  const bubbleColorClasses =
    message.role === 'user'
      ? 'bg-blue-600 text-white ml-auto mr-2'
      : 'bg-gray-100 text-gray-900 dark:bg-gray-800 dark:text-gray-100 ml-2 mr-auto'

  return (
    <MessagePrimitive.Root>
      <div
        className={`mb-4 ${
          message.role === 'user' ? 'text-right' : 'text-left'
        }`}
      >
        {/* Show reasoning timeline for assistant messages with steps - moved above content */}
        {hasSteps && (
          <div className="mb-3">
            <ReasoningTimeline
              messageId={message.id}
              stepCount={stepData.stepCount}
              isStreaming={stepData.isStreaming}
              liveSteps={stepData.steps}
            />
          </div>
        )}

        <div
          className={`${bubbleBaseClasses} ${bubblePadding} ${bubbleColorClasses} group`}
        >
          {/* Copy icon - positioned at bottom-right, only visible on hover */}
          <button
            onClick={handleCopy}
            className="absolute bottom-2 right-2 text-xs text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white dark:bg-gray-800 rounded p-1 shadow-sm border border-gray-200 dark:border-gray-600"
            title="Copy message"
          >
            <FiCopy className="w-3 h-3" />
          </button>

          {/* Message content with optional collapse */}
          <div className="whitespace-pre-wrap break-words">
            {/* If there's no final text but there are tool parts, render parts directly */}
            {!hasTextContent ? (
              <MessagePrimitive.Parts components={{ tools: toolComponents }} />
            ) : collapsed && isLong ? (
              <>
                <div className="overflow-hidden max-h-60">
                  {/* approx 15rem */}
                  {/* Provide empty components object to avoid undefined error inside MessagePrimitive.Parts */}
                  <MessagePrimitive.Content
                    components={{ tools: toolComponents }}
                  />
                </div>
                {/* Gradient overlay */}
                <div className="absolute bottom-0 left-0 w-full h-10 bg-gradient-to-b from-transparent to-inherit pointer-events-none rounded-b-lg" />
                <button
                  onClick={() => setCollapsed(false)}
                  className="mt-2 flex items-center gap-1 text-xs text-blue-500 hover:underline"
                >
                  Show more <FiChevronDown className="w-3 h-3" />
                </button>
              </>
            ) : (
              <>
                <MessagePrimitive.Content
                  components={{ tools: toolComponents }}
                />
                {isLong && (
                  <button
                    onClick={() => setCollapsed(true)}
                    className="mt-2 flex items-center gap-1 text-xs text-blue-500 hover:underline"
                  >
                    Show less <FiChevronUp className="w-3 h-3" />
                  </button>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </MessagePrimitive.Root>
  )
}

/**
 * Provider component for initial messages context
 */
export const InitialMessagesProvider: React.FC<{
  children: React.ReactNode
  initialMessages: ExtendedMessage[]
}> = ({ children, initialMessages }) => {
  return (
    <InitialMessagesContext.Provider value={initialMessages}>
      {children}
    </InitialMessagesContext.Provider>
  )
}
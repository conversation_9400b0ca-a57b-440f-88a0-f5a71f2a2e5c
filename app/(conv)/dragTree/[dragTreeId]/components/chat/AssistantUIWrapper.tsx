'use client'

import React from 'react'
import { useChatRuntime } from '@assistant-ui/react-ai-sdk'
import {
  AssistantRuntimeProvider,
  ThreadPrimitive,
  ComposerPrimitive,
} from '@assistant-ui/react'
import {
  CustomMessage,
  InitialMessagesProvider,
  type ExtendedMessage,
} from './messages/CustomMessage'

import { createLogger } from '@/libs/debug'

const logger = createLogger('ASSISTANT_UI')

/**
 * Props for AssistantUIWrapper component
 */
type AssistantUIWrapperProps = {
  conversationId?: string
  initialMessages?: ExtendedMessage[]
  onMessageFinish?: (message: any) => void
  // New props for conversation creation
  dragTreeId?: string
  onConversationCreated?: (conversationId: string) => void
  onError?: (error: string) => void
}

/**
 * Assistant-UI wrapper component for chat interface
 * Provides a modern, robust chat UI to replace the legacy implementation
 */
export default function AssistantUIWrapper({
  conversationId: initialConversationId,
  initialMessages = [],
  onMessageFinish,
  dragTreeId,
  onConversationCreated,
  onError,
}: AssistantUIWrapperProps) {
  // Extract pagination props with defaults
  // Legacy pagination props are no longer used; kept for backward compatibility.

  // assistant-ui will manage live tool-step streaming via a reducer – no props needed

  // Local state: conversation management, messages and pagination
  const [conversationId, setConversationId] = React.useState<
    string | undefined
  >(initialConversationId)
  const [isCreatingConversation, setIsCreatingConversation] =
    React.useState<boolean>(false)
  const [initialMessagesState, setInitialMessagesState] =
    React.useState<ExtendedMessage[]>(initialMessages)
  const [isHistoryLoading, setIsHistoryLoading] = React.useState<boolean>(false)
  const [paginationCursor, setPaginationCursor] = React.useState<
    string | undefined
  >(undefined)
  const [hasMoreHistory, setHasMoreHistory] = React.useState<boolean>(false)

  // Critical: Track initialization state to prevent race condition
  const [isInitializing, setIsInitializing] = React.useState<boolean>(
    !!initialConversationId // If we have a conversationId, we need to load its history first
  )

  // Create conversation if not provided
  React.useEffect(() => {
    const createConversation = async () => {
      if (conversationId || !dragTreeId) {
        return // Already have conversation or missing required data
      }

      logger.info('Creating new conversation for drag tree', { dragTreeId })
      setIsCreatingConversation(true)

      try {
        const res = await fetch('/api/aipane/conversations', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            contextEntityType: 'drag_tree',
            contextEntityId: dragTreeId,
            title: 'AI Chat Session', // Default title
          }),
        })

        if (!res.ok) {
          const errorText = await res.text()
          throw new Error(
            `Failed to create conversation: ${res.status} ${errorText}`
          )
        }

        const data = await res.json()
        if (data.conversationId) {
          logger.info('Created conversation', {
            conversationId: data.conversationId,
          })
          setConversationId(data.conversationId)
          onConversationCreated?.(data.conversationId)
        } else {
          throw new Error('No conversationId returned from API')
        }
      } catch (err) {
        logger.error('Failed to create conversation', err)
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to initialize chat'
        onError?.(errorMessage)
      } finally {
        setIsCreatingConversation(false)
      }
    }

    createConversation()
  }, [conversationId, dragTreeId, onConversationCreated, onError])

  // Fetch initial conversation history if conversationId is provided
  React.useEffect(() => {
    if (!conversationId) {
      // No conversation to load, initialization complete
      setIsInitializing(false)
      return
    }

    async function fetchConversation() {
      try {
        setIsHistoryLoading(true)
        const res = await fetch(
          `/api/aipane/conversations/${conversationId}?limit=50&includeSteps=true`,
          { cache: 'no-store' }
        )
        if (!res.ok) {
          throw new Error(`Failed to load conversation: ${res.status}`)
        }
        const data = await res.json()
        // data.messages expected from API – guard in case
        const msgs = (data?.messages ?? []) as ExtendedMessage[]
        setInitialMessagesState(msgs)
        setHasMoreHistory(data?.pagination?.hasMore ?? false)
        setPaginationCursor(data?.pagination?.nextCursor)
      } catch (err) {
        logger.error('Conversation fetch failed', err)
        // Even on error, we should complete initialization to show the chat interface
      } finally {
        setIsHistoryLoading(false)
        setIsInitializing(false) // Critical: Mark initialization as complete
      }
    }

    fetchConversation()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [conversationId])

  // Helper to load more history using cursor pagination
  const loadMoreHistory = React.useCallback(async () => {
    if (!conversationId || !paginationCursor) return

    try {
      setIsHistoryLoading(true)
      const res = await fetch(
        `/api/aipane/conversations/${conversationId}?limit=50&cursor=${paginationCursor}&includeSteps=true`,
        { cache: 'no-store' }
      )
      if (!res.ok) {
        throw new Error(`Failed to paginate conversation: ${res.status}`)
      }
      const data = await res.json()
      const newMsgs = (data?.messages ?? []) as ExtendedMessage[]
      // Prepend older messages
      setInitialMessagesState(prev => [...newMsgs, ...prev])
      setHasMoreHistory(data?.pagination?.hasMore ?? false)
      setPaginationCursor(data?.pagination?.nextCursor)
    } catch (err) {
      logger.error('Pagination fetch failed', err)
    } finally {
      setIsHistoryLoading(false)
    }
  }, [conversationId, paginationCursor])

  // Convert our message format to assistant-ui format whenever initialMessagesState changes
  const formattedMessages = React.useMemo(
    () =>
      initialMessagesState.map(msg => ({
        id: msg.id,
        role: msg.role.toLowerCase() as 'user' | 'assistant' | 'system',
        content:
          ('cleanContent' in msg ? (msg as any).cleanContent : null) ||
          msg.content,
        createdAt: msg.createdAt ? new Date(msg.createdAt) : new Date(),
        stepCount: msg.stepCount,
        isStreaming: msg.isStreaming,
      })),
    [initialMessagesState]
  )

  // Debug initial messages step data
  logger.debug('Initial messages with step data:', {
    messages: initialMessagesState.map(msg => ({
      id: msg.id,
      role: msg.role,
      stepCount: msg.stepCount,
      hasSteps: !!msg.stepCount && msg.stepCount > 0,
    })),
  })

  // Steps reducer attaches tool-call / tool-result chunks to the message being streamed
  const stepsReducer = React.useCallback((chunk: any, thread: any) => {
    if (!chunk || !thread) return
    if (chunk.type === 'tool-call' || chunk.type === 'tool-result') {
      const lastMsg = thread.messages[thread.messages.length - 1]
      if (!lastMsg) return
      lastMsg.steps = lastMsg.steps || []
      lastMsg.steps.push(chunk)
    }
  }, [])

  // Set up chat runtime with API endpoint - only after initialization is complete
  const runtime = useChatRuntime({
    api: '/api/aipane/chat',
    initialMessages: formattedMessages,
    body: {
      conversationId,
    },
    onFinish: onMessageFinish,
    reducers: [stepsReducer],
  } as any)

  // Memoised message component with integrated tool/step handling
  const MessageWithIntegratedSteps = React.useCallback(
    (props: any) => <CustomMessage {...props} />,
    []
  )

  // Show loading state while creating conversation or initializing
  if (isCreatingConversation || isInitializing) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="flex flex-col items-center gap-3 text-gray-500">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
          <span className="text-sm font-medium">
            {isCreatingConversation
              ? 'Initializing chat...'
              : 'Loading conversation...'}
          </span>
        </div>
      </div>
    )
  }

  // Don't render chat if no conversation ID is available
  if (!conversationId) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="flex flex-col items-center gap-3 text-gray-500">
          <span className="text-sm font-medium">No conversation available</span>
        </div>
      </div>
    )
  }

  return (
    <InitialMessagesProvider initialMessages={initialMessagesState}>
      <AssistantRuntimeProvider runtime={runtime}>
        <div className="flex flex-col h-full">
          {/* Main chat thread with integrated pagination */}
          <div className="flex-1 min-h-0">
            <ThreadPrimitive.Root className="h-full min-h-0">
              <ThreadPrimitive.Viewport className="flex h-full flex-col overflow-y-auto scroll-smooth">
                {/* Load more messages button at top */}
                {hasMoreHistory && (
                  <div className="sticky top-0 z-10 bg-white border-b border-gray-200 shadow-sm">
                    <div className="flex items-center justify-center py-3">
                      {isHistoryLoading ? (
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500" />
                          <span>Loading more messages...</span>
                        </div>
                      ) : (
                        <button
                          onClick={loadMoreHistory}
                          className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700 font-medium px-4 py-2 rounded-lg hover:bg-blue-50 transition-colors"
                        >
                          <svg
                            className="w-4 h-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 15l7-7 7 7"
                            />
                          </svg>
                          <span>Load more messages</span>
                        </button>
                      )}
                    </div>
                  </div>
                )}

                <div className="flex-1 px-4 pt-8">
                  <ThreadPrimitive.Messages
                    components={{ Message: MessageWithIntegratedSteps }}
                  />
                </div>

                <div className="sticky bottom-0 mt-3 flex w-full flex-col items-center justify-end bg-white pb-4 px-4">
                  <ThreadPrimitive.ScrollToBottom className="mb-2">
                    <button className="bg-white hover:bg-gray-50 border border-gray-200 text-gray-600 hover:text-gray-800 rounded-full p-2 shadow-md transition-colors">
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 14l-7 7m0 0l-7-7m7 7V3"
                        />
                      </svg>
                    </button>
                  </ThreadPrimitive.ScrollToBottom>
                  <ComposerPrimitive.Root className="relative w-full max-w-2xl mx-auto bg-gray-50 rounded-xl border border-gray-200 focus-within:border-blue-500 transition-colors">
                    <ComposerPrimitive.Input
                      placeholder="Message AI Assistant..."
                      className="w-full px-4 py-3 pr-12 bg-transparent focus:outline-none resize-none text-gray-900 placeholder-gray-500"
                      rows={1}
                    />
                    <ComposerPrimitive.Send className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg p-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                        />
                      </svg>
                    </ComposerPrimitive.Send>
                  </ComposerPrimitive.Root>
                </div>
              </ThreadPrimitive.Viewport>
            </ThreadPrimitive.Root>
          </div>
        </div>
      </AssistantRuntimeProvider>
    </InitialMessagesProvider>
  )
}

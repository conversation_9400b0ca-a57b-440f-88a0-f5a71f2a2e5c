'use client'

import React from 'react'
import { useChatRuntime } from '@assistant-ui/react-ai-sdk'
import {
  AssistantRuntimeProvider,
  ThreadPrimitive,
  ComposerPrimitive,
} from '@assistant-ui/react'
import {
  CustomMessage,
  InitialMessagesProvider,
  type ExtendedMessage,
} from './messages/CustomMessage'
import { InfiniteScrollContainer } from './InfiniteScrollContainer'
import { createLogger } from '@/libs/debug'

const logger = createLogger('ASSISTANT_UI')

/**
 * Props for AssistantUIWrapper component
 */
type AssistantUIWrapperProps = {
  conversationId?: string
  initialMessages?: ExtendedMessage[]
  onMessageFinish?: (message: any) => void
  // New props for conversation creation
  dragTreeId?: string
  onConversationCreated?: (conversationId: string) => void
  onError?: (error: string) => void
  // Pagination support (legacy – will be removed once assistant-ui history is fully used)
  pagination?: {
    hasMore: boolean
    isLoading: boolean
    onLoadMore: () => void
  }
  // Streaming props kept for backward compatibility but no longer required
  streaming?: {
    steps: any[]
    isActive: boolean
  }
}

/**
 * Assistant-UI wrapper component for chat interface
 * Provides a modern, robust chat UI to replace the legacy implementation
 */
export default function AssistantUIWrapper({
  conversationId: initialConversationId,
  initialMessages = [],
  onMessageFinish,
  dragTreeId,
  onConversationCreated,
  onError,
  pagination,
}: AssistantUIWrapperProps) {
  // Extract pagination props with defaults
  // Legacy pagination props are no longer used; kept for backward compatibility.

  // assistant-ui will manage live tool-step streaming via a reducer – no props needed

  // Local state: conversation management, messages and pagination
  const [conversationId, setConversationId] = React.useState<
    string | undefined
  >(initialConversationId)
  const [isCreatingConversation, setIsCreatingConversation] =
    React.useState<boolean>(false)
  const [initialMessagesState, setInitialMessagesState] =
    React.useState<ExtendedMessage[]>(initialMessages)
  const [isHistoryLoading, setIsHistoryLoading] = React.useState<boolean>(false)
  const [paginationCursor, setPaginationCursor] = React.useState<
    string | undefined
  >(undefined)
  const [hasMoreHistory, setHasMoreHistory] = React.useState<boolean>(false)

  // Create conversation if not provided
  React.useEffect(() => {
    const createConversation = async () => {
      if (conversationId || !dragTreeId) {
        return // Already have conversation or missing required data
      }

      logger.info('Creating new conversation for drag tree', { dragTreeId })
      setIsCreatingConversation(true)

      try {
        const res = await fetch('/api/aipane/conversations', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            contextEntityType: 'drag_tree',
            contextEntityId: dragTreeId,
            title: 'AI Chat Session', // Default title
          }),
        })

        if (!res.ok) {
          const errorText = await res.text()
          throw new Error(
            `Failed to create conversation: ${res.status} ${errorText}`
          )
        }

        const data = await res.json()
        if (data.conversationId) {
          logger.info('Created conversation', {
            conversationId: data.conversationId,
          })
          setConversationId(data.conversationId)
          onConversationCreated?.(data.conversationId)
        } else {
          throw new Error('No conversationId returned from API')
        }
      } catch (err) {
        logger.error('Failed to create conversation', err)
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to initialize chat'
        onError?.(errorMessage)
      } finally {
        setIsCreatingConversation(false)
      }
    }

    createConversation()
  }, [conversationId, dragTreeId, onConversationCreated, onError])

  // Fetch initial conversation history if conversationId is provided
  React.useEffect(() => {
    if (!conversationId) return

    async function fetchConversation() {
      try {
        setIsHistoryLoading(true)
        const res = await fetch(
          `/api/aipane/conversations/${conversationId}?limit=50&includeSteps=true`,
          { cache: 'no-store' }
        )
        if (!res.ok) {
          throw new Error(`Failed to load conversation: ${res.status}`)
        }
        const data = await res.json()
        // data.messages expected from API – guard in case
        const msgs = (data?.messages ?? []) as ExtendedMessage[]
        setInitialMessagesState(msgs)
        setHasMoreHistory(data?.pagination?.hasMore ?? false)
        setPaginationCursor(data?.pagination?.nextCursor)
      } catch (err) {
        logger.error('Conversation fetch failed', err)
      } finally {
        setIsHistoryLoading(false)
      }
    }

    fetchConversation()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [conversationId])

  // Helper to load more history using cursor pagination
  const loadMoreHistory = React.useCallback(async () => {
    if (!conversationId || !paginationCursor) return

    try {
      setIsHistoryLoading(true)
      const res = await fetch(
        `/api/aipane/conversations/${conversationId}?limit=50&cursor=${paginationCursor}&includeSteps=true`,
        { cache: 'no-store' }
      )
      if (!res.ok) {
        throw new Error(`Failed to paginate conversation: ${res.status}`)
      }
      const data = await res.json()
      const newMsgs = (data?.messages ?? []) as ExtendedMessage[]
      // Prepend older messages
      setInitialMessagesState(prev => [...newMsgs, ...prev])
      setHasMoreHistory(data?.pagination?.hasMore ?? false)
      setPaginationCursor(data?.pagination?.nextCursor)
    } catch (err) {
      logger.error('Pagination fetch failed', err)
    } finally {
      setIsHistoryLoading(false)
    }
  }, [conversationId, paginationCursor])

  // Convert our message format to assistant-ui format whenever initialMessagesState changes
  const formattedMessages = React.useMemo(
    () =>
      initialMessagesState.map(msg => ({
        id: msg.id,
        role: msg.role.toLowerCase() as 'user' | 'assistant' | 'system',
        content:
          ('cleanContent' in msg ? (msg as any).cleanContent : null) ||
          msg.content,
        createdAt: msg.createdAt ? new Date(msg.createdAt) : new Date(),
        stepCount: msg.stepCount,
        isStreaming: msg.isStreaming,
      })),
    [initialMessagesState]
  )

  // Debug initial messages step data
  logger.debug('Initial messages with step data:', {
    messages: initialMessagesState.map(msg => ({
      id: msg.id,
      role: msg.role,
      stepCount: msg.stepCount,
      hasSteps: !!msg.stepCount && msg.stepCount > 0,
    })),
  })

  // Steps reducer attaches tool-call / tool-result chunks to the message being streamed
  const stepsReducer = React.useCallback((chunk: any, thread: any) => {
    if (!chunk || !thread) return
    if (chunk.type === 'tool-call' || chunk.type === 'tool-result') {
      const lastMsg = thread.messages[thread.messages.length - 1]
      if (!lastMsg) return
      lastMsg.steps = lastMsg.steps || []
      lastMsg.steps.push(chunk)
    }
  }, [])

  // Set up chat runtime with API endpoint
  const runtime = useChatRuntime({
    api: '/api/aipane/chat',
    initialMessages: formattedMessages,
    body: {
      conversationId,
    },
    onFinish: onMessageFinish,
    reducers: [stepsReducer],
  } as any)

  // Memoised message component – no extra props needed now
  const MessageWithLiveSteps = React.useCallback(
    (props: any) => <CustomMessage {...props} />,
    []
  )

  // Show loading state while creating conversation
  if (isCreatingConversation) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="flex flex-col items-center gap-3 text-gray-500">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
          <span className="text-sm font-medium">Initializing chat...</span>
        </div>
      </div>
    )
  }

  // Don't render chat if no conversation ID is available
  if (!conversationId) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="flex flex-col items-center gap-3 text-gray-500">
          <span className="text-sm font-medium">No conversation available</span>
        </div>
      </div>
    )
  }

  return (
    <InitialMessagesProvider initialMessages={initialMessagesState}>
      <AssistantRuntimeProvider runtime={runtime}>
        <div className="flex flex-col h-full">
          {/* Main chat thread with InfiniteScrollContainer */}
          <div className="flex-1 min-h-0">
            <InfiniteScrollContainer
              hasMore={hasMoreHistory}
              isLoading={isHistoryLoading}
              onLoadMore={loadMoreHistory}
              className="h-full"
              autoScrollToBottom={true}
            >
              <ThreadPrimitive.Root className="h-full min-h-0">
                <ThreadPrimitive.Messages
                  components={{ Message: MessageWithLiveSteps }}
                />
              </ThreadPrimitive.Root>
            </InfiniteScrollContainer>
          </div>

          {/* Input composer */}
          <div className="border-t bg-background p-4">
            <ComposerPrimitive.Root className="relative flex items-end bg-gray-50 rounded-xl border border-gray-200 focus-within:border-blue-500 transition-colors">
              <ComposerPrimitive.Input
                placeholder="Message AI Assistant..."
                className="flex-1 resize-y overflow-auto max-h-60 min-h-[44px] bg-transparent border-none shadow-none focus:ring-0 focus:outline-none text-gray-900 placeholder-gray-500 px-4 py-3 text-sm leading-relaxed"
                rows={1}
              />
              <ComposerPrimitive.Send className="ml-2 mr-3 mb-3 w-8 h-8 p-0 rounded-lg transition-all duration-200 flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white shadow-sm hover:shadow-md disabled:bg-gray-200 disabled:text-gray-400 disabled:cursor-not-allowed">
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                  />
                </svg>
              </ComposerPrimitive.Send>
            </ComposerPrimitive.Root>
          </div>
        </div>
      </AssistantRuntimeProvider>
    </InitialMessagesProvider>
  )
}

'use client'

import React from 'react'
import { useChatRuntime } from '@assistant-ui/react-ai-sdk'
import {
  AssistantRuntimeProvider,
  ThreadPrimitive,
  ComposerPrimitive,
  useThread,
  useAssistantRuntime,
  AssistantRuntime,
} from '@assistant-ui/react'
import {
  CustomMessage,
  InitialMessagesProvider,
  type ExtendedMessage,
} from './messages/CustomMessage'

import { createLogger } from '@/libs/debug'

const logger = createLogger('ASSISTANT_UI')

/**
 * Props for AssistantUIWrapper component
 */
type AssistantUIWrapperProps = {
  conversationId?: string
  initialMessages?: ExtendedMessage[]
  onMessageFinish?: (message: any) => void
  // New props for conversation creation
  dragTreeId?: string
  onConversationCreated?: (conversationId: string) => void
  onError?: (error: string) => void
}

/**
 * Assistant-UI wrapper component for chat interface
 * Provides a modern, robust chat UI to replace the legacy implementation
 */
export default function AssistantUIWrapper({
  conversationId: initialConversationId,
  initialMessages = [],
  onMessageFinish,
  dragTreeId,
  onConversationCreated,
  onError,
}: AssistantUIWrapperProps) {
  // Debug: Log component mount and props
  console.log(
    '🚀 [AssistantUIWrapper] Component mounted/rendered with props:',
    {
      initialConversationId,
      initialMessagesCount: initialMessages.length,
      dragTreeId,
    }
  )

  React.useEffect(() => {
    console.log('🚀 [AssistantUIWrapper] Props received:', {
      initialConversationId,
      initialMessagesCount: initialMessages.length,
      dragTreeId,
    })
  }, [initialConversationId, initialMessages.length, dragTreeId])
  // Extract pagination props with defaults
  // Legacy pagination props are no longer used; kept for backward compatibility.

  // assistant-ui will manage live tool-step streaming via a reducer – no props needed

  // Local state: conversation management, messages and pagination
  const [conversationId, setConversationId] = React.useState<
    string | undefined
  >(initialConversationId)
  const [isCreatingConversation, setIsCreatingConversation] =
    React.useState<boolean>(false)
  const [initialMessagesState, setInitialMessagesState] =
    React.useState<ExtendedMessage[]>(initialMessages)
  const [isHistoryLoading, setIsHistoryLoading] = React.useState<boolean>(false)
  const [paginationCursor, setPaginationCursor] = React.useState<
    string | undefined
  >(undefined)
  const [hasMoreHistory, setHasMoreHistory] = React.useState<boolean>(false)

  // Track if we're still loading initial conversation data
  const [hasLoadedInitialData, setHasLoadedInitialData] =
    React.useState<boolean>(
      !initialConversationId // If no initial conversation, we're already "loaded"
    )

  // Debug logging for initialization state
  React.useEffect(() => {
    logger.debug('AssistantUIWrapper initialization state', {
      initialConversationId,
      conversationId,
      hasLoadedInitialData,
      isCreatingConversation,
      initialMessagesCount: initialMessagesState.length,
    })
  }, [
    initialConversationId,
    conversationId,
    hasLoadedInitialData,
    isCreatingConversation,
    initialMessagesState.length,
  ])

  // Fallback: Ensure we don't get stuck loading
  React.useEffect(() => {
    if (!hasLoadedInitialData && !isCreatingConversation && !isHistoryLoading) {
      const timeout = setTimeout(() => {
        logger.warn('Data loading timeout - marking as loaded', {
          conversationId,
          initialConversationId,
        })
        setHasLoadedInitialData(true)
      }, 5000) // 5 second timeout

      return () => clearTimeout(timeout)
    }
  }, [
    hasLoadedInitialData,
    isCreatingConversation,
    isHistoryLoading,
    conversationId,
    initialConversationId,
  ])

  // Create conversation if not provided
  React.useEffect(() => {
    const createConversation = async () => {
      if (conversationId || !dragTreeId) {
        return // Already have conversation or missing required data
      }

      logger.info('Creating new conversation for drag tree', { dragTreeId })
      setIsCreatingConversation(true)

      try {
        const res = await fetch('/api/aipane/conversations', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            contextEntityType: 'drag_tree',
            contextEntityId: dragTreeId,
            title: 'AI Chat Session', // Default title
          }),
        })

        if (!res.ok) {
          const errorText = await res.text()
          throw new Error(
            `Failed to create conversation: ${res.status} ${errorText}`
          )
        }

        const data = await res.json()
        if (data.conversationId) {
          logger.info('Created conversation', {
            conversationId: data.conversationId,
          })
          setConversationId(data.conversationId)
          onConversationCreated?.(data.conversationId)
        } else {
          throw new Error('No conversationId returned from API')
        }
      } catch (err) {
        logger.error('Failed to create conversation', err)
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to initialize chat'
        onError?.(errorMessage)
      } finally {
        setIsCreatingConversation(false)
      }
    }

    createConversation()
  }, [conversationId, dragTreeId, onConversationCreated, onError])

  // Fetch initial conversation history if conversationId is provided
  React.useEffect(() => {
    if (!conversationId) {
      // No conversation to load, mark as loaded
      setHasLoadedInitialData(true)
      return
    }

    async function fetchConversation() {
      try {
        setIsHistoryLoading(true)
        const res = await fetch(
          `/api/aipane/conversations/${conversationId}?limit=50&includeSteps=true`,
          { cache: 'no-store' }
        )
        if (!res.ok) {
          throw new Error(`Failed to load conversation: ${res.status}`)
        }
        const data = await res.json()
        // data.messages expected from API – guard in case
        const msgs = (data?.messages ?? []) as ExtendedMessage[]

        console.log('📨 [AssistantUIWrapper] Raw messages from API:', {
          count: msgs.length,
          messages: msgs.map(msg => ({
            id: msg.id,
            role: msg.role,
            content:
              msg.content?.substring(0, 100) +
              (msg.content?.length > 100 ? '...' : ''),
            contentLength: msg.content?.length || 0,
            hasContent: !!msg.content,
            stepCount: msg.stepCount,
          })),
        })

        setInitialMessagesState(msgs)
        setHasMoreHistory(data?.pagination?.hasMore ?? false)
        setPaginationCursor(data?.pagination?.nextCursor)
      } catch (err) {
        logger.error('Conversation fetch failed', err)
        // Even on error, we should mark as loaded to show the chat interface
      } finally {
        setIsHistoryLoading(false)
        setHasLoadedInitialData(true) // Mark as loaded regardless of success/failure
      }
    }

    fetchConversation()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [conversationId])

  // Helper to load more history using cursor pagination
  const loadMoreHistory = React.useCallback(async () => {
    if (!conversationId || !paginationCursor) return

    try {
      setIsHistoryLoading(true)
      const res = await fetch(
        `/api/aipane/conversations/${conversationId}?limit=50&cursor=${paginationCursor}&includeSteps=true`,
        { cache: 'no-store' }
      )
      if (!res.ok) {
        throw new Error(`Failed to paginate conversation: ${res.status}`)
      }
      const data = await res.json()
      const newMsgs = (data?.messages ?? []) as ExtendedMessage[]
      // Prepend older messages
      setInitialMessagesState(prev => [...newMsgs, ...prev])
      setHasMoreHistory(data?.pagination?.hasMore ?? false)
      setPaginationCursor(data?.pagination?.nextCursor)
    } catch (err) {
      logger.error('Pagination fetch failed', err)
    } finally {
      setIsHistoryLoading(false)
    }
  }, [conversationId, paginationCursor])

  // Convert our message format to assistant-ui format whenever initialMessagesState changes
  const formattedMessages = React.useMemo(() => {
    const formatted = initialMessagesState.map(msg => {
      const content =
        ('cleanContent' in msg ? (msg as any).cleanContent : null) ||
        msg.content ||
        ''

      return {
        id: msg.id,
        role: msg.role.toLowerCase() as 'user' | 'assistant' | 'system',
        content: content,
        createdAt: msg.createdAt ? new Date(msg.createdAt) : new Date(),
        stepCount: msg.stepCount,
        isStreaming: msg.isStreaming,
      }
    })

    console.log(
      '🔄 [AssistantUIWrapper] Formatted messages for assistant-ui:',
      {
        originalCount: initialMessagesState.length,
        formattedCount: formatted.length,
        messages: formatted.map(msg => ({
          id: msg.id,
          role: msg.role,
          contentLength: msg.content?.length || 0,
          hasContent: !!msg.content,
          contentPreview:
            msg.content?.substring(0, 50) +
            (msg.content?.length > 50 ? '...' : ''),
        })),
      }
    )

    // Also log the full formatted messages for debugging
    console.log('🔍 [AssistantUIWrapper] Full formatted messages:', formatted)

    return formatted
  }, [initialMessagesState])

  // Debug initial messages step data
  logger.debug('Initial messages with step data:', {
    messages: initialMessagesState.map(msg => ({
      id: msg.id,
      role: msg.role,
      stepCount: msg.stepCount,
      hasSteps: !!msg.stepCount && msg.stepCount > 0,
    })),
  })

  // Steps reducer attaches tool-call / tool-result chunks to the message being streamed
  const stepsReducer = React.useCallback((chunk: any, thread: any) => {
    if (!chunk || !thread) return
    if (chunk.type === 'tool-call' || chunk.type === 'tool-result') {
      const lastMsg = thread.messages[thread.messages.length - 1]
      if (!lastMsg) return
      lastMsg.steps = lastMsg.steps || []
      lastMsg.steps.push(chunk)
    }
  }, [])

  // Set up chat runtime with API endpoint - use real formatted messages
  const runtime = useChatRuntime({
    api: '/api/aipane/chat',
    initialMessages: formattedMessages,
    body: {
      conversationId,
    },
    onFinish: onMessageFinish,
    reducers: [stepsReducer],
  } as any)

  // Debug runtime initialization
  React.useEffect(() => {
    console.log('⚡ [AssistantUIWrapper] Chat runtime initialized with:', {
      conversationId,
      initialMessagesCount: formattedMessages.length,
      hasLoadedInitialData,
      runtimeMessagesCount: formattedMessages.length,
    })
  }, [conversationId, formattedMessages.length, hasLoadedInitialData])

  // Try to manually append messages to the runtime if initialMessages doesn't work
  React.useEffect(() => {
    if (formattedMessages.length > 0 && runtime) {
      console.log(
        '🔧 [AssistantUIWrapper] Attempting to manually append messages to runtime'
      )
      try {
        // This is a workaround - we'll try to access the runtime's internal state
        // and manually set the messages if the initialMessages approach doesn't work
        const runtimeState = (runtime as any).getState?.()
        if (runtimeState) {
          console.log('🔧 [AssistantUIWrapper] Runtime state:', runtimeState)
        }
      } catch (err) {
        console.log(
          '🔧 [AssistantUIWrapper] Could not access runtime state:',
          err
        )
      }
    }
  }, [formattedMessages, runtime])

  // Memoised message component with integrated tool/step handling
  const MessageWithIntegratedSteps = React.useCallback(
    (props: any) => <CustomMessage {...props} />,
    []
  )

  // Show loading state only while creating conversation
  if (isCreatingConversation) {
    console.log('⏳ [AssistantUIWrapper] Showing loading state:', {
      isCreatingConversation,
    })
    return (
      <div className="flex items-center justify-center h-full">
        <div className="flex flex-col items-center gap-3 text-gray-500">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
          <span className="text-sm font-medium">Initializing chat...</span>
        </div>
      </div>
    )
  }

  // Don't render chat if no conversation ID is available
  if (!conversationId) {
    console.log('❌ [AssistantUIWrapper] No conversation ID available')
    return (
      <div className="flex items-center justify-center h-full">
        <div className="flex flex-col items-center gap-3 text-gray-500">
          <span className="text-sm font-medium">No conversation available</span>
        </div>
      </div>
    )
  }

  console.log('✅ [AssistantUIWrapper] Rendering chat interface with:', {
    conversationId,
    messagesCount: initialMessagesState.length,
    hasLoadedInitialData,
  })

  return (
    <InitialMessagesProvider initialMessages={initialMessagesState}>
      <AssistantRuntimeProvider
        key={`${conversationId}-${initialMessagesState.length}`}
        runtime={runtime}
      >
        <div className="flex flex-col h-full">
          {/* Main chat thread with integrated pagination */}
          <div className="flex-1 min-h-0">
            <ThreadPrimitive.Root className="h-full min-h-0">
              <ThreadPrimitive.Viewport className="flex h-full flex-col overflow-y-auto scroll-smooth">
                {/* Load more messages button at top */}
                {hasMoreHistory && (
                  <div className="sticky top-0 z-10 bg-white border-b border-gray-200 shadow-sm">
                    <div className="flex items-center justify-center py-3">
                      {isHistoryLoading ? (
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500" />
                          <span>Loading more messages...</span>
                        </div>
                      ) : (
                        <button
                          onClick={loadMoreHistory}
                          className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700 font-medium px-4 py-2 rounded-lg hover:bg-blue-50 transition-colors"
                        >
                          <svg
                            className="w-4 h-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 15l7-7 7 7"
                            />
                          </svg>
                          <span>Load more messages</span>
                        </button>
                      )}
                    </div>
                  </div>
                )}

                <div className="flex-1 px-4 pt-8">
                  <ThreadPrimitive.Messages
                    components={{ Message: MessageWithIntegratedSteps }}
                  />
                </div>

                <div className="sticky bottom-0 mt-3 flex w-full flex-col items-center justify-end bg-white pb-4 px-4">
                  <ThreadPrimitive.ScrollToBottom className="mb-2 bg-white hover:bg-gray-50 border border-gray-200 text-gray-600 hover:text-gray-800 rounded-full p-2 shadow-md transition-colors">
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 14l-7 7m0 0l-7-7m7 7V3"
                      />
                    </svg>
                  </ThreadPrimitive.ScrollToBottom>
                  <ComposerPrimitive.Root className="relative w-full max-w-2xl mx-auto bg-gray-50 rounded-xl border border-gray-200 focus-within:border-blue-500 transition-colors">
                    <ComposerPrimitive.Input
                      placeholder="Message AI Assistant..."
                      className="w-full px-4 py-3 pr-12 bg-transparent focus:outline-none resize-none text-gray-900 placeholder-gray-500"
                      rows={1}
                    />
                    <ComposerPrimitive.Send className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg p-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                        />
                      </svg>
                    </ComposerPrimitive.Send>
                  </ComposerPrimitive.Root>
                </div>
              </ThreadPrimitive.Viewport>
            </ThreadPrimitive.Root>
          </div>
        </div>
      </AssistantRuntimeProvider>
    </InitialMessagesProvider>
  )
}

'use client'

import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react'
import { FiMessageCircle, FiSettings, FiEdit2 } from 'react-icons/fi'
import { useUIStore } from '@/app/stores/ui_store'
import { useAiPaneStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useAiPaneStore'
import { useAssetStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useAssetStore'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { convertTiptapJsonToMarkdown } from '@/app/components/editor/utils'
import type { Tab } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore'
// Select imports removed (not needed for read-only display)
import { useNavigationStore } from '@/app/stores/navigation_store'
import toast from 'react-hot-toast'
import { useTabStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore'
import { featureFlags } from '../../lib/feature-flags'
import AssistantUIWrapper from '../chat/AssistantUIWrapper'

type ExtendedMessage = {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  createdAt: string
  stepCount?: number
  isStreaming?: boolean
}

type ChatTabContentProps = {
  tab: Tab
  dragTreeId: string
}

const ChatTabContent: React.FC<ChatTabContentProps> = ({ tab, dragTreeId }) => {
  const [showContextDialog, setShowContextDialog] = useState<boolean>(false)
  // Maintain local copy of chat messages for asset creation logic
  const [messages, setMessages] = useState<ExtendedMessage[]>([])
  const [conversationError, setConversationError] = useState<string | null>(
    null
  )
  const [showSettingsModal, setShowSettingsModal] = useState<boolean>(false)
  const [isEditingTitle, setIsEditingTitle] = useState<boolean>(false)
  const [editableTitle, setEditableTitle] = useState<string>(tab.title || '')

  // API endpoint selection
  const useRealLLMAPI = useUIStore(state => state.useRealLLMAPI)
  const apiEndpoint = useRealLLMAPI
    ? '/api/aipane/chat'
    : '/api/aipane/chat-simulator'

  // Get drag tree context
  const nodeContent = useDragTreeStore(state => state.nodeContent)
  const findNodeById = useDragTreeStore(state => state.findNodeById)
  const { settings } = useAiPaneStore()
  const { navigateToTreeNode } = useNavigationStore()
  // Asset helpers (declare early to avoid TDZ in callbacks)
  const { addAsset, updateAsset } = useAssetStore()

  // Update function to mutate aiPaneData (e.g. clear prompt after first use)
  const updateTabAiPaneData = useTabStore(state => state.updateTabAiPaneData)
  const updateTabTitle = useTabStore(state => state.updateTabTitle)
  const updateAiPaneData = useTabStore(state => state.updateTabAiPaneData)

  // Create conversation lazily if it doesn't exist yet
  useEffect(() => {
    const createConversation = async () => {
      if (tab.aiPaneData?.conversationId) {
        console.log(
          '💬 [ChatTab] Conversation already exists:',
          tab.aiPaneData.conversationId
        )
        return
      }

      console.log('💬 [ChatTab] Creating new conversation for tab:', tab.id)
      try {
        const res = await fetch('/api/aipane/conversations', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            contextEntityType: 'drag_tree',
            contextEntityId: dragTreeId,
            title: 'AI Chat Session', // Default title
          }),
        })
        if (!res.ok) {
          const errorText = await res.text()
          throw new Error(
            `Failed to create conversation: ${res.status} ${errorText}`
          )
        }
        const data = await res.json()
        if (data.conversationId) {
          console.log('💬 [ChatTab] Created conversation:', data.conversationId)
          updateTabAiPaneData(tab.id, { conversationId: data.conversationId })
        } else {
          throw new Error('No conversationId returned from API')
        }
      } catch (err) {
        console.error('💬 [ChatTab] Failed to create conversation:', err)
        setConversationError(
          err instanceof Error ? err.message : 'Failed to initialize chat'
        )
        // Don't silently fail - show error to user
        toast.error('Failed to initialize chat. Please try again.')
      }
    }
    createConversation()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // Navigate to context item in outline
  const navigateToContextItem = useCallback(
    (nodeId: string) => {
      navigateToTreeNode(nodeId)
      setShowContextDialog(false)
      console.log('Navigating to node:', nodeId)
    },
    [navigateToTreeNode]
  )

  // Memoize context to prevent unnecessary re-renders
  const contextContent = useCallback(
    () => getContextContent(),
    [tab.aiPaneData?.contextIds, nodeContent]
  )

  // Conversation will be handled entirely by AssistantUIWrapper; expose message finish callback
  const handleMessageFinish = useCallback(
    (message: ExtendedMessage) => {
      if (!message) return

      setMessages(prev => {
        const updated = [...prev, message]

        // Asset creation / update logic when assistant finishes
        const isAssistantFinal =
          message.role === 'assistant' && !message.isStreaming
        if (isAssistantFinal && tab.aiPaneData?.conversationId) {
          if (!assetIdRef.current) {
            const firstUser = updated.find(m => m.role === 'user')
            if (firstUser) {
              const chatTitle = generateChatAssetTitle(firstUser.content)
              const newAssetId = addAsset({
                id: tab.aiPaneData.conversationId,
                title: chatTitle,
                content: '',
                type: 'chat',
                model: tab.aiPaneData?.model || 'gpt-4.1',
                prompt: firstUser.content,
                contextIds: tab.aiPaneData?.contextIds || [],
                dragTreeId,
                messages: updated
                  .filter(m => m.role === 'user' || m.role === 'assistant')
                  .map(m => ({
                    role: m.role as 'user' | 'assistant',
                    content: m.content,
                  })),
                persistedInDb: true,
                isContentLoaded: true,
                createdAt: new Date(),
                viewed: false,
              })
              updateTabTitle(tab.id, chatTitle)
              updateTabAiPaneData(tab.id, { assetId: newAssetId })
              assetIdRef.current = newAssetId
            }
          } else if (assetIdRef.current) {
            updateAsset(assetIdRef.current, {
              messages: updated
                .filter(m => m.role === 'user' || m.role === 'assistant')
                .map(m => ({
                  role: m.role as 'user' | 'assistant',
                  content: m.content,
                })),
              updatedAt: new Date(),
            })
          }
        }

        return updated
      })
    },
    [
      addAsset,
      updateAsset,
      updateTabTitle,
      updateTabAiPaneData,
      tab.aiPaneData,
      tab.id,
      dragTreeId,
    ]
  )

  // Debug log received messages from AssistantUIWrapper
  useEffect(() => {
    if (messages.length > 0) {
      console.log('[ChatTabContent] Collected messages:', messages)
    }
  }, [messages])

  // ------------------------------------------------------------------
  // 🔗  Asset integration – turn every chat conversation into an Asset so
  //      the user can revisit past chats from the Asset sidebar.
  // ------------------------------------------------------------------

  const assetIdRef = useRef<string | null>(tab.aiPaneData?.assetId || null)

  useEffect(() => {
    // Create asset when we have conversation ID and at least one assistant response
    // Use tab.aiPaneData?.conversationId instead of conversationData?.id to avoid race condition
    const conversationId =
      tab.aiPaneData?.conversationId || conversationData?.id
    if (!conversationId) {
      console.log(
        '💾 [ChatTab] No conversation ID available for asset creation'
      )
      return
    }

    // Check if asset already created (tab or global store)
    if (assetIdRef.current) {
      console.log('💾 [ChatTab] Asset already created:', assetIdRef.current)
      return
    }

    const lastMsg = messages[messages.length - 1]
    if (!lastMsg || lastMsg.role !== 'assistant' || lastMsg.isStreaming) {
      console.log('💾 [ChatTab] Waiting for assistant response to complete')
      return
    }

    // Ensure there is at least one user message to derive prompt/title
    const firstUser = messages.find(m => m.role === 'user')
    if (!firstUser) {
      console.log('💾 [ChatTab] No user message found for asset creation')
      return
    }

    // Avoid duplicate creation across hot reloads
    if (assets.some(a => a.id === conversationId)) {
      console.log('💾 [ChatTab] Asset already exists in store:', conversationId)
      assetIdRef.current = conversationId
      return
    }

    console.log(
      '💾 [ChatTab] Creating chat asset for conversation:',
      conversationId
    )
    const chatTitle =
      conversationData?.title || generateChatAssetTitle(firstUser.content)

    const newAssetId = addAsset({
      id: conversationId,
      title: chatTitle,
      content: '', // Chat assets rely on messages array instead of single blob
      type: 'chat',
      model: tab.aiPaneData?.model || 'gpt-4.1',
      prompt: firstUser.content,
      contextIds: tab.aiPaneData?.contextIds || [],
      dragTreeId: dragTreeId,
      messages: messages
        .filter(m => m.role === 'user' || m.role === 'assistant')
        .map(m => ({
          role: m.role as 'user' | 'assistant',
          content: m.content,
        })),
      persistedInDb: true,
      isContentLoaded: true,
      createdAt: new Date(),
      viewed: false,
    })

    // Update the tab title so it matches the new asset title (mirrors Generate behaviour)
    updateTabTitle(tab.id, chatTitle)

    // Store assetId on the tab to prevent re-adding
    updateTabAiPaneData(tab.id, { assetId: newAssetId })
    assetIdRef.current = newAssetId
    console.log('💾 [ChatTab] Chat asset created successfully:', newAssetId)
  }, [
    tab.aiPaneData?.conversationId,
    conversationData?.id,
    conversationData?.title,
    messages,
    assets,
    addAsset,
    dragTreeId,
    tab.aiPaneData?.model,
    tab.aiPaneData?.contextIds,
    updateTabAiPaneData,
    tab.id,
    updateTabTitle,
  ])

  // Removed prevLoadingRef logic (legacy streaming state)

  // Update editable title when tab title changes
  useEffect(() => {
    setEditableTitle(tab.title || '')
  }, [tab.title])

  // Handle title save
  const handleTitleSave = () => {
    const title = editableTitle.trim()
    if (title.length === 0) return
    updateTabTitle(tab.id, title)
    // Update corresponding asset title if exists
    if (assetIdRef.current) {
      updateAsset(assetIdRef.current, { title })
    }
    // Persist to server
    if (tab.aiPaneData?.conversationId) {
      fetch(
        `/api/aipane/conversations/${tab.aiPaneData.conversationId}/title`,
        {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ title }),
        }
      ).catch(err => console.error('Failed to update title:', err))
    }
    setIsEditingTitle(false)
  }

  // Get context content from selected drag tree nodes
  function getContextContent(): string {
    if (!tab.aiPaneData?.contextIds) return ''

    return tab.aiPaneData.contextIds
      .map(nodeId => {
        const contentMap = nodeContent.get(nodeId)
        if (!contentMap || contentMap.size === 0) return ''
        const firstContentItem = Array.from(contentMap.values())[0]
        let text = (firstContentItem as any)?.contentText || ''
        if (typeof text === 'object' && text.type === 'doc') {
          text = convertTiptapJsonToMarkdown(text)
        } else if (typeof text === 'string') {
          try {
            const parsed = JSON.parse(text)
            if (parsed && parsed.type === 'doc') {
              text = convertTiptapJsonToMarkdown(parsed)
            }
          } catch (e) {}
        }
        return text as string
      })
      .filter(Boolean)
      .join('\n\n')
  }

  // removed legacy initialization effect – AssistantUIWrapper handles conversation history

  // Removed legacy cleanMessages – messages formatting handled inside AssistantUIWrapper

  // Scrolling handled by AssistantUIWrapper

  // Removed copyMessage helper (legacy UI)

  // Format timestamp
  const formatTime = (date: Date): string => {
    // Defensively handle invalid dates
    if (!(date instanceof Date) || isNaN(date.getTime())) {
      return ''
    }
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  // Truncate long content with "See more" option
  type TruncatedContentProps = {
    content: string
    maxLines?: number
  }

  const TruncatedContentComponent: React.FC<TruncatedContentProps> = ({
    content,
    maxLines = 8,
  }) => {
    const [isExpanded, setIsExpanded] = useState(false)
    const lines = content.split('\n')
    const shouldTruncate = lines.length > maxLines

    if (!shouldTruncate || isExpanded) {
      return <span className="whitespace-pre-wrap">{content}</span>
    }

    const truncatedContent = lines.slice(0, maxLines).join('\n')

    return (
      <div>
        <span className="whitespace-pre-wrap">{truncatedContent}</span>
        <button
          onClick={() => setIsExpanded(true)}
          className="text-blue-500 hover:text-blue-700 text-sm mt-2 block"
        >
          ... See more ({lines.length - maxLines} more lines)
        </button>
      </div>
    )
  }

  const TruncatedContent = React.memo(TruncatedContentComponent)

  return (
    <div className="h-full min-h-0 flex flex-col bg-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-green-100 rounded-lg">
            <FiMessageCircle className="w-5 h-5 text-green-600" />
          </div>
          <div>
            {isEditingTitle ? (
              <Input
                value={editableTitle}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setEditableTitle(e.target.value)
                }
                onBlur={handleTitleSave}
                onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                  if (e.key === 'Enter') {
                    e.preventDefault()
                    handleTitleSave()
                  }
                }}
                className="h-7 text-sm px-2 py-1"
                autoFocus
              />
            ) : (
              <div className="flex items-center space-x-1 group">
                <h2 className="text-lg font-semibold text-gray-900">
                  {editableTitle || 'AI Chat'}
                </h2>
                <button
                  onClick={() => setIsEditingTitle(true)}
                  className="opacity-0 group-hover:opacity-100 transition-opacity text-gray-500 hover:text-gray-700"
                  title="Edit title"
                >
                  <FiEdit2 className="w-4 h-4" />
                </button>
              </div>
            )}
            {/* Optional subheading removed per UX request */}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowContextDialog(true)}
            className="flex items-center space-x-1 text-sm text-gray-500 hover:text-gray-700 px-2 py-1 rounded hover:bg-gray-100"
            title="Context items"
          >
            <span>Using {tab.aiPaneData?.contextIds.length || 0} context</span>
          </button>
          <button
            onClick={() => setShowSettingsModal(true)}
            className="p-2 text-gray-500 hover:text-gray-700 rounded hover:bg-gray-100"
            title="Chat settings"
          >
            <FiSettings className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Chat interface using AssistantUIWrapper */}
      <div className="flex-1 min-h-0">
        {conversationError ? (
          <div className="flex items-center justify-center h-full">
            <div className="flex flex-col items-center gap-3 text-red-500 max-w-md text-center">
              <FiMessageCircle className="w-8 h-8" />
              <span className="text-sm font-medium">
                Failed to initialize chat
              </span>
              <span className="text-xs text-gray-500">{conversationError}</span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setConversationError(null)
                  window.location.reload()
                }}
              >
                Retry
              </Button>
            </div>
          </div>
        ) : (
          <AssistantUIWrapper
            conversationId={tab.aiPaneData?.conversationId}
            onMessageFinish={handleMessageFinish}
          />
        )}
      </div>

      {/* Context Items Dialog */}
      <Dialog open={showContextDialog} onOpenChange={setShowContextDialog}>
        <DialogContent className="max-w-2xl max-h-[70vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>
              Context Items ({tab.aiPaneData?.contextIds.length || 0})
            </DialogTitle>
          </DialogHeader>
          <div className="overflow-y-auto space-y-2 max-h-[60vh]">
            {tab.aiPaneData?.contextIds?.map((nodeId, index) => {
              const node = findNodeById(nodeId)
              const nodeLabel = node?.label || `Item ${index + 1}`

              const contentMap = nodeContent.get(nodeId)
              let previewText = 'No content available'

              if (contentMap && contentMap.size > 0) {
                const firstContentItem = Array.from(contentMap.values())[0]
                let text = (firstContentItem as any)?.contentText || ''

                // Convert content to readable format
                if (typeof text === 'object' && text.type === 'doc') {
                  text = convertTiptapJsonToMarkdown(text)
                } else if (typeof text === 'string') {
                  try {
                    const parsed = JSON.parse(text)
                    if (parsed && parsed.type === 'doc') {
                      text = convertTiptapJsonToMarkdown(parsed)
                    }
                  } catch (e) {}
                }
                previewText =
                  typeof text === 'string' ? text : 'Content available'
              }

              return (
                <button
                  key={nodeId}
                  onClick={() => navigateToContextItem(nodeId)}
                  className="w-full text-left p-3 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors duration-200 group"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded font-mono">
                          {index + 1}
                        </span>
                        <div className="text-sm font-medium text-gray-900 group-hover:text-blue-700 truncate">
                          {nodeLabel}
                        </div>
                      </div>
                      <div className="text-xs text-gray-500 mt-2 line-clamp-3">
                        {previewText.substring(0, 200)}
                        {previewText.length > 200 && '...'}
                      </div>
                    </div>
                    <div className="ml-2 text-xs text-gray-400 group-hover:text-blue-500 flex-shrink-0">
                      Click to navigate
                    </div>
                  </div>
                </button>
              )
            })}

            {(!tab.aiPaneData?.contextIds ||
              tab.aiPaneData.contextIds.length === 0) && (
              <div className="text-center text-gray-500 py-8">
                No context items selected
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Settings Dialog */}
      <Dialog open={showSettingsModal} onOpenChange={setShowSettingsModal}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Chat Settings</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="text-sm flex items-center justify-between">
              <span className="font-medium text-gray-700">Model</span>
              <span className="text-gray-900">
                {tab.aiPaneData?.model || 'gpt-4.1'}
              </span>
            </div>

            <div className="text-sm flex items-center justify-between">
              <span className="font-medium text-gray-700">Messages</span>
              <span className="text-gray-900">{messages.length}</span>
            </div>

            {tab.aiPaneData?.contextIds &&
              tab.aiPaneData.contextIds.length > 0 && (
                <div className="text-sm flex items-center justify-between">
                  <span className="font-medium text-gray-700">
                    Context Items
                  </span>
                  <button
                    onClick={() => {
                      setShowContextDialog(true)
                      setShowSettingsModal(false)
                    }}
                    className="text-blue-600 underline hover:text-blue-800"
                  >
                    {tab.aiPaneData.contextIds.length} selected
                  </button>
                </div>
              )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default React.memo(ChatTabContent)

import {
  getCachedMessages,
  setCachedMessages,
} from '@/libs/cache/conversation-cache'
import type { CachedMessage } from '@/types/chat'
import prisma from '@/app/libs/prismadb'
const { encode } = require('@nem035/gpt-3-encoder')

// Rough token estimator: 1 token ≈ 4 chars (works well for English)
function estimateTokens(text: string): number {
  return encode(text).length
}

// Build context: system prompt + first user message + tail until budget
export async function buildContextMessages(
  conversationId: string,
  newUserMessage: { role: 'user'; content: string },
  budgetTokens = 100_000
): Promise<{
  messages: { role: 'user' | 'assistant' | 'system'; content: string }[]
}> {
  // try cache first
  let cached = getCachedMessages(conversationId)
  let all: CachedMessage[]
  if (!cached) {
    const rows = await prisma.aiMessage.findMany({
      where: { conversationId },
      orderBy: { createdAt: 'asc' },
      select: { id: true, role: true, content: true, createdAt: true },
    })
    all = rows.map(r => ({
      id: r.id,
      role: r.role.toLowerCase() as 'user' | 'assistant' | 'system',
      content: r.content,
      createdAt: r.createdAt,
    }))
    setCachedMessages(conversationId, all)
  } else {
    all = cached
  }

  if (!all || all.length === 0) {
    // brand-new conversation, just system + new user
    return {
      messages: [
        {
          role: 'system',
          content:
            'You are a helpful AI assistant in a drag tree chat interface. Provide comprehensive and well-structured responses using markdown formatting when appropriate.',
        },
        newUserMessage,
      ],
    }
  }

  const systemPrompt = {
    role: 'system' as const,
    content:
      'You are a helpful AI assistant in a drag tree chat interface. Provide comprehensive and well-structured responses using markdown formatting when appropriate.',
  }

  const firstUserOrSystem = all[0]

  // Always keep first historical message
  const context: CachedMessage[] = [firstUserOrSystem]

  // calculate tokens used by mandatory parts
  let used =
    estimateTokens(systemPrompt.content) +
    estimateTokens(firstUserOrSystem.content) +
    estimateTokens(newUserMessage.content)

  // Walk tail backwards
  for (let i = all.length - 1; i > 0; i--) {
    const msg = all[i]
    const t = estimateTokens(msg.content)
    if (used + t > budgetTokens) break
    used += t
    context.unshift(msg) // build from back to front
  }

  const final = [systemPrompt, ...context, newUserMessage]

  return { messages: final }
}

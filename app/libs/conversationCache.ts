/**
 * Conversation cache - backward compatibility layer
 * 
 * This file provides backward compatibility for existing imports while
 * delegating to the new generic cache system.
 * 
 * @deprecated Use @/libs/cache/conversation-cache directly in new code
 */

// Re-export the new implementations for backward compatibility
export {
  getCachedMessages,
  getCachedConversation,
  setCachedMessages,
  setCachedConversation,
  deleteCachedConversation,
  clearConversationCache,
  getConversationCacheStats,
} from '@/libs/cache/conversation-cache'

// Re-export types from the centralized location
export type {
  CachedMessage,
  CachedConversation,
} from '@/types/chat'